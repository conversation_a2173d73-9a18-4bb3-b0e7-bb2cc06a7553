#!/usr/bin/env python3
"""
Enhanced Battery Timeline Pipeline - Fixed 4-Phase Hybrid Approach

This script creates comprehensive battery timelines using a fixed 4-phase hybrid approach:

Phase 1: Data Loading and Cleaning (Hybrid CSV + PostgreSQL)
- Load repair events, snapshots, vehicle info from CSV files
- Load daily stats activity data from CSV for performance
- Load VIN to vehicle_id mapping from PostgreSQL for accuracy
- Clean and validate data with comprehensive validation rules
- Build caches for efficient processing

Phase 2: Fixed Timeline Building (Battery-Centric with Chronological Processing)
- Process ALL events in chronological order (ascending date, earliest first)
- Process each battery's events chronologically (ascending date order)
- Earlier events establish timelines first, preventing chronological overlaps
- Track active periods by VIN to handle cross-VIN transfers properly
- End periods in source vehicles when transfers occur
- Start new periods in destination vehicles for installations
- Handle orphaned removals with proper start date inference and vehicle-level validation
- FIXED: Prevent missing periods, phantom overlaps, and chronological conflicts

Phase 3: Comprehensive Validation and Quality Assurance
- Validate timelines against vehicle activity patterns using CSV data
- Perform final deduplication and conflict resolution
- Assign lifecycle stages with activity-based validation
- Generate quality metrics and export results

Phase 4: Vehicle Activity Validation and Timeline Extension
- Validate that active vehicles have assigned batteries (vehicles can't run without batteries)
- Identify gaps where vehicles are active but have no current battery in timeline
- Extend battery periods or create new periods based on vehicle activity data
- Handle missing battery assignments and cross-vehicle transfers
- Ensure timeline completeness against real-world vehicle usage

CRITICAL FIXES APPLIED:
- Missing Period Bug: All installations now create periods regardless of matching removals
- Cross-VIN Sequential Logic Bug: Removed flawed cross-VIN sequential chaining
- Phantom Overlap Creation: Chronological processing prevents invalid date overlaps
- Proper Transfer Handling: Active period tracking ensures clean cross-VIN transfers
- Activity-Battery Consistency: Ensures all active vehicles have battery assignments

Key Features:
- Hybrid data approach: CSV for performance + PostgreSQL for accuracy
- FIXED battery-centric processing with chronological event ordering
- Pre-loaded daily stats from CSV for fast in-memory access
- VIN to vehicle_id mapping from PostgreSQL vehicles table
- Comprehensive validation rules for km data
- Proper cross-VIN transfer detection and handling
- Vehicle activity integration for better gap inference
- Enhanced validation using fleet patterns (avg 20km/day)
- Activity-based timeline extension and validation

Data Sources:
- daily_stats.csv: Vehicle activity data (vehicle_id, date, km_start, km_end)
- PostgreSQL vehicles table: VIN to vehicle_id mapping
- CSV files: Repair events and vehicle snapshots

Output format:
battery_id | vin | start_date | end_date | adjusted_start_date | adjusted_end_date | confidence | source | lifecycle_stage | km_validated | activity_validated
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Set
import warnings
import psycopg2
from sqlalchemy import create_engine, text
import os

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

VALIDATION_CONSTANTS = {
    "activity_threshold_days": 90,  # Number of days to consider a vehicle as recently active
    "battery_extension_days": 360,  # Max days to extend a battery period backward/forward
    "max_gap_battery_transfer": 180,  # Max days allowed for a battery transfer gap
    "max_gap_battery_extension": 720,  # Max days allowed for a battery extension gap
    "min_battery_period_days": 1,  # Minimum days for a valid battery period
    "overlap_tolerance_days": 0,  # Days of overlap allowed between battery periods
    "chronological_tolerance_days": 0,  # Days allowed for chronological errors
    # Phase 5 validation parameters
    "validation_passed": False,  # Overall validation status
    "critical_errors": 0,  # Count of critical errors
    "warnings": 0,  # Count of warnings
    "battery_uniqueness_violations": 0,  # Count of battery uniqueness violations
    "chronological_errors": 0,  # Count of chronological errors
    "overlap_violations": 0,  # Count of overlap violations
    "missing_data_periods": 0,  # Count of periods with missing data
    "activity_mismatches": 0,  # Count of activity mismatches
    "transfer_logic_errors": 0,  # Count of transfer logic errors
}


class BatteryTimelinePipeline:
    """
    Battery Timeline Pipeline with Streamlined 3-Phase Approach

    Phase 1: Data Loading and Cleaning
    Phase 2: Unified Timeline Building (Battery-Centric)
    Phase 3: Comprehensive Validation and Quality Assurance
    Phase 4: Vehicle Activity Validation and Timeline Extension
    """

    def __init__(
        self,
        hv_repair_file: str,
        working_matching_vehicles_file: str,
        working_unique_vehicles_file: str,
        daily_stats_csv_file: str = "daily_stats.csv",
        db_connection_string: str = None,
        test_batteries: List[str] = None,
    ):
        """Initialize with file paths and optional database connection."""
        self.hv_repair_file = hv_repair_file
        self.working_matching_vehicles_file = working_matching_vehicles_file
        self.working_unique_vehicles_file = working_unique_vehicles_file
        self.daily_stats_csv_file = daily_stats_csv_file
        self.test_batteries = (
            test_batteries  # Filter for specific batteries if provided
        )

        # Database connection (only needed for VIN to vehicle_id mapping)
        self.db_connection_string = (
            db_connection_string or self._get_default_db_connection()
        )
        self.db_engine = None

        # Fleet patterns for validation
        self.avg_km_per_day = 20
        self.avg_soc_usage_per_day = 12  # percentage points

        # Phase 1 data containers
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.cleaned_events = []
        self.vehicle_info_cache = {}
        self.vehicle_activity_cache = {}
        self.vin_to_vehicle_id = {}
        self.global_battery_cache = {}

        # Daily stats data (loaded from CSV)
        self.daily_stats_df = None
        self.daily_stats_by_vehicle = {}  # Pre-indexed by vehicle_id for fast lookup

        # Phase 2 data containers
        self.battery_timelines = []
        self.timeline_stats = {}

        # Phase 3 data containers
        self.final_timeline = []
        self.quality_stats = {}

        # Phase 4 data containers
        self.extension_periods = []
        self.extension_stats = {}

        # Processing statistics
        self.phase_stats = {}

    def _get_default_db_connection(self) -> str:
        """Get default database connection string from environment or config."""
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")

        return f"postgresql://{user}:{password}@{host}:{port}/{database}"

    # =================================================================
    # PHASE 1: DATA LOADING AND CLEANING
    # =================================================================

    def phase1_load_and_clean_data(self) -> Dict:
        """Phase 1: Load and clean all data sources with comprehensive validation."""
        logger.info("=== PHASE 1: DATA LOADING AND CLEANING ===")

        # Database connection already verified in pre-flight check
        # Use existing connection or initialize if not done yet
        if not self.db_engine:
            # Check if we're in legacy mode (db_engine explicitly set to None)
            if hasattr(self, "_legacy_mode") and self._legacy_mode:
                self._initialize_database_connection_optional()
            else:
                self._initialize_database_connection()

        # Load core data
        self._load_hv_repair_data()
        self._load_working_vehicles_data()
        self._load_daily_stats_csv()
        self._load_vehicle_activity_data()

        # Clean and validate
        self._clean_repair_events()
        self._build_vehicle_info_cache()
        self._build_global_battery_cache()
        self._validate_data_quality()

        phase1_stats = {
            "raw_repair_records": len(self.hv_repair_df),
            "raw_vehicle_records": len(self.working_vehicles_df),
            "clean_events": len(self.cleaned_events),
            "unique_vins": len(self.vehicle_info_cache),
            "vehicles_with_activity": len(self.vin_to_vehicle_id),
            "global_batteries": len(self.global_battery_cache),
            "data_quality_score": self._calculate_data_quality_score(),
        }

        logger.info(f"Phase 1 complete: {phase1_stats}")
        return phase1_stats

    def _initialize_database_connection(self):
        """Initialize PostgreSQL database connection."""
        try:
            self.db_engine = create_engine(self.db_connection_string)
            # Test connection
            with self.db_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {self.db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _initialize_database_connection_optional(self):
        """Initialize PostgreSQL database connection with fallback (legacy method)."""
        try:
            self.db_engine = create_engine(self.db_connection_string)
            # Test connection
            with self.db_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.warning(f"❌ Database connection failed: {e}")
            logger.warning("Proceeding without activity validation")
            self.db_engine = None

    def _load_hv_repair_data(self) -> pd.DataFrame:
        """Load and perform basic cleaning of HV repair data."""
        logger.info(f"Loading HV repair data from: {self.hv_repair_file}")

        self.hv_repair_df = pd.read_csv(self.hv_repair_file, sep=",")
        logger.info(f"Loaded {len(self.hv_repair_df):,} HV repair records")

        # Clean dates
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Filter valid records
        initial_count = len(self.hv_repair_df)
        self.hv_repair_df = self.hv_repair_df.dropna(
            subset=["vin", "effective_date", "action"]
        )
        self.hv_repair_df = self.hv_repair_df[self.hv_repair_df["vin"].str.len() > 10]

        # Filter for test batteries if specified
        if self.test_batteries:
            logger.info(f"Filtering for test batteries: {self.test_batteries}")
            test_battery_mask = self.hv_repair_df["battery_id_old"].isin(
                self.test_batteries
            ) | self.hv_repair_df["battery_id_new"].isin(self.test_batteries)
            self.hv_repair_df = self.hv_repair_df[test_battery_mask]
            logger.info(
                f"After test battery filtering: {len(self.hv_repair_df):,} records"
            )

        filtered_count = initial_count - len(self.hv_repair_df)
        if filtered_count > 0:
            logger.warning(f"Filtered out {filtered_count:,} invalid HV repair records")

        self.hv_repair_df = self.hv_repair_df.sort_values(["effective_date", "vin"])
        logger.info(f"Clean HV repair data: {len(self.hv_repair_df):,} records")

        return self.hv_repair_df

    def _load_working_vehicles_data(self) -> pd.DataFrame:
        """Load and combine working vehicles data from both files."""
        logger.info(f"Loading working vehicles data...")
        logger.info(f"  Matching vehicles file: {self.working_matching_vehicles_file}")
        logger.info(f"  Unique vehicles file: {self.working_unique_vehicles_file}")

        # Load both files
        matching_df = pd.read_csv(self.working_matching_vehicles_file)
        unique_df = pd.read_csv(self.working_unique_vehicles_file)

        logger.info(f"Loaded {len(matching_df):,} matching vehicle records")
        logger.info(f"Loaded {len(unique_df):,} unique vehicle records")

        # Combine both dataframes
        self.working_vehicles_df = pd.concat(
            [matching_df, unique_df], ignore_index=True
        )
        logger.info(
            f"Combined working vehicles data: {len(self.working_vehicles_df):,} records"
        )

        # Clean erstzulassung date (vehicle rollout date)
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        # Clean battery IDs
        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Clean VIN and AKZ
        for col in ["vin", "akz"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = (
                    self.working_vehicles_df[col].astype(str).str.strip()
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        return self.working_vehicles_df

    def _load_daily_stats_csv(self):
        """Load daily stats data from CSV file and pre-index by vehicle_id for fast lookup."""
        logger.info(f"Loading daily stats data from CSV: {self.daily_stats_csv_file}")

        try:
            # Load CSV with proper data types
            self.daily_stats_df = pd.read_csv(
                self.daily_stats_csv_file,
                dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
                parse_dates=["date"],
            )

            logger.info(f"Loaded {len(self.daily_stats_df):,} daily stats records")

            # Pre-index by vehicle_id for fast lookup
            logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
            self.daily_stats_by_vehicle = {}

            for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
                # Sort by date for each vehicle
                vehicle_data = group.sort_values("date").copy()
                self.daily_stats_by_vehicle[vehicle_id] = vehicle_data

            logger.info(
                f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
            )

            # Memory optimization: clear the main dataframe as we now have indexed data
            # Keep a reference for statistics but the main lookups will use the indexed version
            total_vehicles_with_data = len(self.daily_stats_by_vehicle)
            logger.info(
                f"Daily stats CSV loaded successfully: {total_vehicles_with_data:,} vehicles with activity data"
            )

        except Exception as e:
            logger.error(f"Failed to load daily stats CSV: {e}")
            raise Exception(f"Could not load daily stats CSV file: {e}")

    def _load_vehicle_activity_data(self):
        """Load VIN to vehicle_id mapping from database (daily_stats now loaded from CSV)."""
        if not self.db_engine:
            logger.warning(
                "No database connection - skipping VIN to vehicle_id mapping"
            )
            return

        try:
            logger.info("Loading VIN to vehicle_id mapping from database...")

            # Load only the VIN to vehicle_id mapping (lightweight)
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(f"Loaded VIN mapping for {len(mapping_df):,} vehicles")

            # Build VIN to vehicle_id mapping
            self.vin_to_vehicle_id = {}
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

            # Pre-cache activity data for erstzulassung dates for performance (now using CSV data)
            self._precache_erstzulassung_activity()

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            # Don't fail completely, just continue without activity data
            self.vin_to_vehicle_id = {}

    def _precache_erstzulassung_activity(self):
        """Pre-cache activity validation for all unique VINs at their erstzulassung dates for performance (using CSV data)."""
        logger.info(
            "Pre-caching activity data for erstzulassung dates from CSV data..."
        )

        # Collect unique VINs with erstzulassung dates
        erstzulassung_dates = {}
        for vin, vehicle_info in self.vehicle_info_cache.items():
            erstzulassung = vehicle_info.get("erstzulassung")
            if (
                erstzulassung
                and not pd.isna(erstzulassung)
                and vin in self.vin_to_vehicle_id
            ):
                erstzulassung_dates[vin] = erstzulassung

        logger.info(
            f"Pre-caching activity data for {len(erstzulassung_dates)} VINs with erstzulassung dates..."
        )

        # Pre-load activity data for these dates using CSV data
        cached_count = 0
        for vin, erstzulassung in erstzulassung_dates.items():
            activity_data = self._query_vehicle_activity_on_date_csv(vin, erstzulassung)
            if activity_data is not None:
                # Store in cache with date key for quick lookup (safely handle NaT)
                try:
                    cache_key = f"{vin}_{erstzulassung.date()}"
                except (TypeError, AttributeError):
                    logger.debug(
                        f"Cannot create cache key for {vin} with erstzulassung {erstzulassung}"
                    )
                    continue
                if not hasattr(self, "erstzulassung_activity_cache"):
                    self.erstzulassung_activity_cache = {}
                self.erstzulassung_activity_cache[cache_key] = activity_data
                cached_count += 1

        logger.info(f"Pre-cached activity data for {cached_count} erstzulassung dates")

    def _query_vehicle_activity_on_date_csv(
        self, vin: str, target_date: datetime
    ) -> Optional[Dict]:
        """Query vehicle activity data for a specific date using CSV data, handling multiple records."""
        if vin not in self.vin_to_vehicle_id:
            return None

        vehicle_id = self.vin_to_vehicle_id[vin]

        # Get vehicle data from pre-indexed CSV data
        if vehicle_id not in self.daily_stats_by_vehicle:
            return None

        vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

        # Filter for specific date
        target_date_str = target_date.date()
        date_data = vehicle_data[vehicle_data["date"].dt.date == target_date_str]

        if date_data.empty:
            return None

        # Check if ANY record shows valid activity (≥2km movement)
        has_valid_activity = False
        valid_records = []
        all_km_starts = []
        all_km_ends = []

        for _, row in date_data.iterrows():
            km_start, km_end = row["km_start"], row["km_end"]

            # Collect all km values for summary
            if pd.notna(km_start):
                all_km_starts.append(km_start)
            if pd.notna(km_end):
                all_km_ends.append(km_end)

            # Check if this individual record shows valid activity
            if (
                pd.notna(km_start)
                and pd.notna(km_end)
                and km_end > km_start
                and km_end - km_start >= 2
            ):
                has_valid_activity = True
                valid_records.append(
                    {
                        "km_start": km_start,
                        "km_end": km_end,
                        "km_diff": km_end - km_start,
                    }
                )

        return {
            "date": target_date_str,
            "km_start": min(all_km_starts) if all_km_starts else None,
            "km_end": max(all_km_ends) if all_km_ends else None,
            "has_km_activity": has_valid_activity,
            "total_records": len(date_data),
            "valid_records": len(valid_records),
            "details": f"Found {len(date_data)} records, {len(valid_records)} with ≥2km activity",
        }

    def _query_vehicle_activity_on_date(
        self, vin: str, target_date: datetime
    ) -> Optional[Dict]:
        """Query vehicle activity data for a specific date using CSV data (wrapper for backward compatibility)."""
        return self._query_vehicle_activity_on_date_csv(vin, target_date)

    def _query_vehicle_activity_on_fly(
        self, vin: str, query_type: str = "summary"
    ) -> Optional[Dict]:
        """Query vehicle activity data on-the-fly using CSV data without caching."""
        if vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Get vehicle data from pre-indexed CSV data
            if vehicle_id not in self.daily_stats_by_vehicle:
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if query_type == "summary":
                # Calculate summary statistics
                total_days = len(vehicle_data)

                if total_days == 0:
                    return None

                # Valid KM data (km_end >= km_start, both >= 0)
                valid_km_mask = (
                    (vehicle_data["km_end"] >= vehicle_data["km_start"])
                    & (vehicle_data["km_end"] >= 0)
                    & (vehicle_data["km_start"] >= 0)
                    & (vehicle_data["km_end"].notna())
                    & (vehicle_data["km_start"].notna())
                )

                valid_km_data = vehicle_data[valid_km_mask]
                avg_daily_km = None

                if len(valid_km_data) > 0:
                    daily_km_diffs = valid_km_data["km_end"] - valid_km_data["km_start"]
                    avg_daily_km = daily_km_diffs.mean()

                # Safely get first and last activity dates, handling NaT values
                first_activity = vehicle_data["date"].min()
                last_activity = vehicle_data["date"].max()

                first_activity_date = None
                last_activity_date = None

                if pd.notna(first_activity):
                    first_activity_date = first_activity.date()

                if pd.notna(last_activity):
                    last_activity_date = last_activity.date()

                return {
                    "vehicle_id": vehicle_id,
                    "total_days": total_days,
                    "first_activity_date": first_activity_date,
                    "last_activity_date": last_activity_date,
                    "avg_daily_km": avg_daily_km,
                }

            elif query_type == "first_active":
                # Find first active date with ≥2km movement
                active_mask = (
                    (vehicle_data["km_end"] - vehicle_data["km_start"] >= 2)
                    & (vehicle_data["km_end"] > vehicle_data["km_start"])
                    & (vehicle_data["km_start"] >= 0)
                    & (vehicle_data["km_end"].notna())
                    & (vehicle_data["km_start"].notna())
                )

                active_data = vehicle_data[active_mask].sort_values("date")

                if len(active_data) > 0:
                    first_row = active_data.iloc[0]
                    first_date = first_row["date"]

                    if pd.notna(first_date):
                        return {
                            "first_active_date": datetime.combine(
                                first_date.date(), datetime.min.time()
                            ),
                            "km_start": first_row["km_start"],
                            "km_end": first_row["km_end"],
                        }
                    else:
                        logger.debug(
                            f"First active date is NaT for vehicle {vehicle_id}"
                        )
                        return None

            return None

        except Exception as e:
            logger.debug(f"Error querying activity data for VIN {vin}: {e}")
            return None

    def _calculate_vehicle_data_quality_from_values(
        self, total_days: int, days_with_km: int, invalid_km_records: int
    ) -> float:
        """Calculate data quality score from individual values (0-1)."""
        if total_days == 0:
            return 0.0

        # Completeness score based on KM data
        km_completeness = days_with_km / total_days if total_days > 0 else 0

        # Validity score based on KM data
        km_validity = 1 - (invalid_km_records / total_days) if total_days > 0 else 0

        # Combined score (100% based on KM data)
        return round((km_completeness * 0.6 + km_validity * 0.4), 3)

    def _calculate_vehicle_data_quality(self, row) -> float:
        """Calculate data quality score for a vehicle based on KM data only (0-1)."""
        if row["total_days"] == 0:
            return 0.0

        # Completeness score based on KM data
        km_completeness = (
            row["days_with_km"] / row["total_days"] if row["total_days"] > 0 else 0
        )

        # Validity score based on KM data
        km_validity = (
            1 - (row["invalid_km_records"] / row["total_days"])
            if row["total_days"] > 0
            else 0
        )

        # Combined score (100% based on KM data)
        return round((km_completeness * 0.6 + km_validity * 0.4), 3)

    def _clean_repair_events(self):
        """Clean and classify repair events with enhanced validation."""
        logger.info("Cleaning and classifying repair events...")

        self.cleaned_events = []

        for _, row in self.hv_repair_df.iterrows():
            # Basic event structure
            event = {
                "date": row["effective_date"],
                "vin": row["vin"],
                "battery_id_old": (
                    row["battery_id_old"] if pd.notna(row["battery_id_old"]) else None
                ),
                "battery_id_new": (
                    row["battery_id_new"] if pd.notna(row["battery_id_new"]) else None
                ),
                "action": row["action"],
                "raw_data": row.to_dict(),
            }

            # Classify event type based on battery IDs
            if event["battery_id_old"] and event["battery_id_new"]:
                event["event_type"] = "change"
                event["clarity"] = "clear"
            elif event["battery_id_new"] and not event["battery_id_old"]:
                event["event_type"] = "install"
                event["clarity"] = "clear"
            elif event["battery_id_old"] and not event["battery_id_new"]:
                event["event_type"] = "remove_or_confirm"
                event["clarity"] = "ambiguous"
            else:
                event["event_type"] = "unclear"
                event["clarity"] = "invalid"

            # Enhanced validation flags
            event["flags"] = []
            if not event["battery_id_old"] and not event["battery_id_new"]:
                event["flags"].append("no_battery_ids")
            if event["date"] < datetime(2020, 1, 1):
                event["flags"].append("old_date")
            if event["date"] > datetime.now():
                event["flags"].append("future_date")

            # Activity validation will be done on-the-fly when needed
            # Re-enable activity validation for comprehensive analysis

            self.cleaned_events.append(event)

        logger.info(f"Cleaned {len(self.cleaned_events)} repair events")

        # Count by clarity
        clarity_counts = {}
        for event in self.cleaned_events:
            clarity = event["clarity"]
            clarity_counts[clarity] = clarity_counts.get(clarity, 0) + 1

        logger.info(f"Event clarity distribution: {clarity_counts}")

    def _build_vehicle_info_cache(self):
        """Build enhanced vehicle information cache."""
        logger.info("Building vehicle information cache...")

        self.vehicle_info_cache = {}

        if self.working_vehicles_df is not None:
            for _, row in self.working_vehicles_df.iterrows():
                vin = row.get("vin")
                if vin and pd.notna(vin):
                    self.vehicle_info_cache[vin] = {
                        "vin": vin,
                        "erstzulassung": row.get("erstzulassung"),
                        "master_battery": row.get("master"),
                        "slave_battery": row.get("slave"),
                        "akz": row.get("akz"),
                    }

        # Add VINs from repair data that aren't in vehicle data
        repair_vins = set(event["vin"] for event in self.cleaned_events)
        vehicle_vins = set(self.vehicle_info_cache.keys())
        missing_vins = repair_vins - vehicle_vins

        for vin in missing_vins:
            self.vehicle_info_cache[vin] = {
                "vin": vin,
                "erstzulassung": None,
                "master_battery": None,
                "slave_battery": None,
                "akz": None,
                "source": "repair_only",
            }

        # Merge with activity data (will be loaded on-demand)
        # Activity data will be loaded when needed via _load_vehicle_activity_on_demand

        logger.info(f"Built cache for {len(self.vehicle_info_cache)} vehicles")

    def _build_global_battery_cache(self):
        """Build global battery cache for cross-VIN transfer tracking."""
        logger.info("Building global battery cache...")

        self.global_battery_cache = {}

        # Process all battery IDs from repair events
        for event in self.cleaned_events:
            for battery_field in ["battery_id_old", "battery_id_new"]:
                battery_id = event.get(battery_field)
                if battery_id:
                    if battery_id not in self.global_battery_cache:
                        self.global_battery_cache[battery_id] = {
                            "battery_id": battery_id,
                            "vins_used": set(),
                            "first_seen": event["date"],
                            "last_seen": event["date"],
                            "install_events": [],
                            "remove_events": [],
                        }

                    cache_entry = self.global_battery_cache[battery_id]
                    cache_entry["vins_used"].add(event["vin"])
                    cache_entry["first_seen"] = min(
                        cache_entry["first_seen"], event["date"]
                    )
                    cache_entry["last_seen"] = max(
                        cache_entry["last_seen"], event["date"]
                    )

                    if battery_field == "battery_id_new":
                        cache_entry["install_events"].append(event)
                    else:
                        cache_entry["remove_events"].append(event)

        # Add snapshot batteries
        for vin, vehicle_info in self.vehicle_info_cache.items():
            for battery_field in ["master_battery", "slave_battery"]:
                battery_id = vehicle_info.get(battery_field)
                if battery_id and pd.notna(battery_id):
                    if battery_id not in self.global_battery_cache:
                        self.global_battery_cache[battery_id] = {
                            "battery_id": battery_id,
                            "vins_used": {vin},
                            "first_seen": vehicle_info.get("erstzulassung")
                            or datetime.min,
                            "last_seen": datetime.now(),
                            "install_events": [],
                            "remove_events": [],
                            "snapshot_only": True,
                        }
                    else:
                        self.global_battery_cache[battery_id]["vins_used"].add(vin)

        logger.info(
            f"Built global cache for {len(self.global_battery_cache)} batteries"
        )

    def _validate_data_quality(self):
        """Enhanced data quality validation."""
        logger.info("Validating data quality...")

        # Activity-based validation
        activity_issues = 0
        for event in self.cleaned_events:
            vin = event["vin"]
            if vin in self.vehicle_activity_cache:
                activity = self.vehicle_activity_cache[vin]

                # Check for repairs during inactive periods
                if self._is_vehicle_inactive_on_date(vin, event["date"]):
                    event["flags"].append("repair_during_inactive_period")
                    activity_issues += 1

        if activity_issues > 0:
            logger.warning(f"Found {activity_issues} repairs during inactive periods")

    def _is_vehicle_inactive_on_date(self, vin: str, date: datetime) -> bool:
        """Check if vehicle was inactive on a specific date based on daily stats CSV data."""
        if vin not in self.vin_to_vehicle_id:
            return False

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Get vehicle data from pre-indexed CSV data
            if vehicle_id not in self.daily_stats_by_vehicle:
                return True  # No data = inactive

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            # Filter for specific date
            target_date_str = date.date()
            date_data = vehicle_data[vehicle_data["date"].dt.date == target_date_str]

            if date_data.empty:
                return True  # No data = inactive

            # Check if km indicates no activity for all records on this date
            # If ANY record shows movement ≥2km, consider vehicle active
            for _, row in date_data.iterrows():
                km_start, km_end = row["km_start"], row["km_end"]

                if pd.notna(km_start) and pd.notna(km_end):
                    if km_end > km_start and (km_end - km_start) >= 2:
                        return False  # Found meaningful activity

            return True  # No meaningful activity found

        except Exception as e:
            logger.debug(f"Error checking activity for {vin} on {date}: {e}")
            return False

    def _calculate_data_quality_score(self) -> float:
        """Calculate overall data quality score (0-1)."""
        if not self.cleaned_events:
            return 0.0

        total_events = len(self.cleaned_events)
        clear_events = len([e for e in self.cleaned_events if e["clarity"] == "clear"])
        flagged_events = len([e for e in self.cleaned_events if e["flags"]])

        # Score based on clarity and flags
        clarity_score = clear_events / total_events
        flag_penalty = flagged_events / total_events

        quality_score = max(0.0, clarity_score - (flag_penalty * 0.5))
        return round(quality_score, 3)

    # =================================================================
    # PHASE 2: UNIFIED TIMELINE BUILDING (BATTERY-CENTRIC)
    # =================================================================

    def phase2_unified_timeline_building(self) -> Dict:
        """Phase 2: Fixed battery-centric timeline building with chronological processing."""
        logger.info("=== PHASE 2: FIXED BATTERY-CENTRIC TIMELINE BUILDING ===")

        # CRITICAL FIX: Sort ALL events by ASCENDING date for chronological processing (earliest events first)
        # This ensures earlier events establish timelines first, preventing chronological overlaps
        sorted_events = sorted(
            self.cleaned_events, key=lambda x: x["date"], reverse=False
        )

        logger.info(
            f"Processing {len(sorted_events)} events in chronological order (earliest first) - FIXED"
        )

        # Extract all unique battery IDs from all events
        all_batteries = set()
        for event in sorted_events:
            if event.get("battery_id_old"):
                all_batteries.add(event["battery_id_old"])
            if event.get("battery_id_new"):
                all_batteries.add(event["battery_id_new"])

        logger.info(f"Found {len(all_batteries)} unique batteries to process")

        # CRITICAL FIX: Process each battery with shared vehicle-level period tracking
        self.battery_timelines = []
        self.vehicle_period_cache = {}  # Track periods by VIN across all batteries
        timeline_stats = {
            "batteries_processed": 0,
            "periods_created": 0,
            "edge_cases_handled": 0,
            "cross_vin_transfers": 0,
            "processing_errors": 0,
        }

        for battery_id in all_batteries:
            try:
                logger.debug(f"🔋 Processing battery: {battery_id}")
                battery_periods = self._build_battery_timeline_pure(
                    battery_id, sorted_events, self.vehicle_period_cache
                )
                self.battery_timelines.extend(battery_periods)

                timeline_stats["batteries_processed"] += 1
                timeline_stats["periods_created"] += len(battery_periods)

                # Count cross-VIN transfers
                vins_in_periods = set(period["vin"] for period in battery_periods)
                if len(vins_in_periods) > 1:
                    timeline_stats["cross_vin_transfers"] += 1

                logger.debug(
                    f"  ✅ Battery {battery_id}: {len(battery_periods)} periods created"
                )

            except Exception as e:
                logger.error(f"❌ Error processing battery {battery_id}: {e}")
                logger.error(
                    f"   Battery {battery_id} events: {[event for event in sorted_events if event.get('battery_id_old') == battery_id or event.get('battery_id_new') == battery_id]}"
                )
                timeline_stats["processing_errors"] += 1
                # Continue processing other batteries
                continue

        # Add snapshot-only batteries that weren't found in repair events
        processed_batteries = set(
            period["battery_id"] for period in self.battery_timelines
        )
        snapshot_periods = self._add_snapshot_only_batteries(processed_batteries)
        self.battery_timelines.extend(snapshot_periods)
        timeline_stats["periods_created"] += len(snapshot_periods)

        self.timeline_stats = timeline_stats
        logger.info(f"Phase 2 complete: {timeline_stats}")
        return timeline_stats

    def _build_battery_timeline_pure(
        self, battery_id: str, all_events: List[Dict], vehicle_period_cache: Dict
    ) -> List[Dict]:
        """Build timeline for a specific battery using fixed battery-centric approach with proper cross-VIN handling."""

        # Filter events for this battery (already sorted by descending date)
        battery_events = [
            event
            for event in all_events
            if event.get("battery_id_old") == battery_id
            or event.get("battery_id_new") == battery_id
        ]

        if not battery_events:
            return []

        # CRITICAL FIX: Filter out events with NaT dates that could cause arithmetic errors
        valid_battery_events = []
        for event in battery_events:
            event_date = event.get("date")
            if event_date is None or pd.isna(event_date):
                logger.warning(
                    f"Skipping event with invalid date for battery {battery_id}: {event}"
                )
                continue
            valid_battery_events.append(event)

        battery_events = valid_battery_events
        if not battery_events:
            logger.warning(
                f"No valid events found for battery {battery_id} after date filtering"
            )
            return []

        logger.debug(
            f"Processing battery {battery_id} with {len(battery_events)} events"
        )

        # CRITICAL FIX: New approach - process all events chronologically to build proper timeline
        # Sort events chronologically (ascending date order) for proper timeline reconstruction
        battery_events.sort(key=lambda x: x["date"])

        periods = []
        processed_events = set()  # Track processed events to avoid duplicates

        # Track active periods by VIN (battery can be in multiple vehicles over time)
        active_periods_by_vin = {}

        # Process events chronologically to build timeline
        for i, event in enumerate(battery_events):
            event_vin = event["vin"]
            event_date = event["date"]
            event_id = f"{event_vin}_{event_date.isoformat()}_{event.get('battery_id_old', 'None')}_{event.get('battery_id_new', 'None')}"

            if event_id in processed_events:
                continue

            logger.debug(
                f"Processing event {i+1}/{len(battery_events)}: {event_date.date()} - VIN: {event_vin}"
            )

            # Handle installation events (battery_id_new == battery_id)
            if event.get("battery_id_new") == battery_id:
                # INSTALLATION: Battery is being installed into this vehicle

                # End any active period in other vehicles (cross-VIN transfer)
                for other_vin, active_period in list(active_periods_by_vin.items()):
                    if other_vin != event_vin and active_period is not None:
                        # End the active period in the other vehicle
                        transfer_end_date = (
                            event_date  # Transfer happens on installation date
                        )

                        # Apply date validation
                        validated_end, end_adjustment = self._find_validated_date(
                            other_vin, transfer_end_date, "backward"
                        )

                        original_end = transfer_end_date
                        adjusted_end = validated_end if validated_end else None

                        # Update the active period with end date
                        active_period["end_date"] = original_end
                        active_period["adjusted_end_date"] = adjusted_end
                        active_period["lifecycle_stage"] = "transferred"

                        # Update duration
                        if active_period["start_date"]:
                            active_period["duration_days"] = (
                                original_end - active_period["start_date"]
                            ).days

                        if active_period["adjusted_start_date"] and adjusted_end:
                            active_period["adjusted_duration_days"] = (
                                adjusted_end - active_period["adjusted_start_date"]
                            ).days

                        # Add transfer note
                        transfer_note = f"Transferred to {event_vin}"
                        if end_adjustment > 0:
                            transfer_note += (
                                f" | Revalidated: end_adjusted_-{end_adjustment}d"
                            )

                        existing_note = active_period.get("note", "")
                        active_period["note"] = (
                            f"{existing_note} | {transfer_note}".strip(" |")
                        )

                        # Finalize the transferred period
                        periods.append(active_period)
                        active_periods_by_vin[other_vin] = None

                        logger.debug(
                            f"  → Ended period in {other_vin} due to transfer to {event_vin}"
                        )

                # CRITICAL FIX: Check if there's already an active period for this battery-VIN pair
                if (
                    event_vin in active_periods_by_vin
                    and active_periods_by_vin[event_vin] is not None
                ):
                    # There's already an active period for this battery in this vehicle
                    existing_period = active_periods_by_vin[event_vin]
                    existing_start = existing_period["start_date"]

                    # Compare dates: use the earlier start date
                    if existing_start is None or event_date < existing_start:
                        # New event date is earlier - validate gap coverage before updating
                        gap_start = event_date
                        gap_end = existing_start if existing_start else datetime.now()

                        # Validate that battery was continuously in this vehicle during gap
                        if self._validate_battery_gap_coverage(
                            battery_id, event_vin, gap_start, gap_end
                        ):
                            logger.info(
                                f"🔧 BATTERY START UPDATE: Updating start date for battery {battery_id} in {event_vin} "
                                f"from {existing_start.date() if existing_start else 'None'} to earlier date {event_date.date()} (gap validated)"
                            )

                            # Apply date validation for the new earlier start date
                            validated_start, start_adjustment = (
                                self._find_validated_date(
                                    event_vin, event_date, "forward"
                                )
                            )

                            # Update the existing period with earlier start date
                            existing_period["start_date"] = event_date
                            existing_period["adjusted_start_date"] = (
                                validated_start if validated_start else None
                            )

                            # Update note to reflect the earlier start date
                            update_note = f"Start date updated to earlier installation: {event_date.date()}"
                            if start_adjustment > 0:
                                update_note += f" | Revalidated: start_adjusted_+{start_adjustment}d"

                            existing_note = existing_period.get("note", "")
                            existing_period["note"] = (
                                f"{existing_note} | {update_note}".strip(" |")
                            )

                            # Update duration if there's an end date
                            if existing_period["end_date"]:
                                existing_period["duration_days"] = (
                                    existing_period["end_date"] - event_date
                                ).days

                            if existing_period[
                                "adjusted_start_date"
                            ] and existing_period.get("adjusted_end_date"):
                                existing_period["adjusted_duration_days"] = (
                                    existing_period["adjusted_end_date"]
                                    - existing_period["adjusted_start_date"]
                                ).days

                            logger.debug(
                                f"  → Updated existing period start date in {event_vin}"
                            )
                        else:
                            logger.warning(
                                f"Gap validation failed for battery {battery_id} in {event_vin}: "
                                f"Cannot use earlier date {event_date.date()} due to gap issues"
                            )
                    else:
                        # Existing start date is earlier or same - keep existing period
                        logger.debug(
                            f"  → Keeping existing earlier start date {existing_start.date()} for battery {battery_id} in {event_vin}"
                        )

                    processed_events.add(event_id)
                    continue

                # Start new period in current vehicle (no existing active period)
                # CRITICAL FIX: Use installation event date directly, don't search for older dates
                start_date = event_date
                method = "installation_event"
                confidence = "high"

                # Apply date validation for start date
                validated_start, start_adjustment = self._find_validated_date(
                    event_vin, start_date, "forward"
                )

                original_start = start_date
                adjusted_start = validated_start if validated_start else None

                # Create note
                install_note = f"Installation: {method}"
                if start_adjustment > 0:
                    install_note += (
                        f" | Revalidated: start_adjusted_+{start_adjustment}d"
                    )

                # Create new active period
                new_period = self._create_period(
                    battery_id,
                    event_vin,
                    original_start,
                    None,  # No end date yet - active period
                    "repair_event",
                    confidence,
                    "active",
                    note=install_note,
                    adjusted_start_date=adjusted_start,
                    adjusted_end_date=None,
                )

                active_periods_by_vin[event_vin] = new_period
                logger.debug(f"  → Started new period in {event_vin}")

                processed_events.add(event_id)

            # Handle removal/replacement events (battery_id_old == battery_id)
            elif event.get("battery_id_old") == battery_id:
                # REMOVAL/REPLACEMENT: Battery is being removed from this vehicle

                if event.get("battery_id_new"):
                    # Clear replacement: battery_id_old -> battery_id_new
                    replacement_type = "replaced"
                    removal_note = f"Replaced with {event.get('battery_id_new')}"
                else:
                    # Ambiguous removal: battery_id_old only
                    # ENHANCED LOGIC: Check what type of event this really is by looking at next battery event
                    next_battery_event = self._find_next_battery_event_in_vehicle(
                        event_vin, event_date, all_events
                    )

                    if (
                        next_battery_event
                        and next_battery_event.get("battery_id_old") == battery_id
                    ):
                        # Next event also has this battery → current event is implied installation
                        is_implied_installation = True
                        logger.info(
                            f"  → Implied installation: next event also has battery {battery_id}"
                        )
                    elif (
                        next_battery_event
                        and next_battery_event.get("battery_id_old") != battery_id
                    ):
                        # Next event has different battery → current event is removal, next battery should start here
                        is_implied_installation = False
                        next_battery_id = next_battery_event.get("battery_id_old")
                        logger.info(
                            f"  → Actual removal: next battery {next_battery_id} should start on {event_date.date()}"
                        )
                    else:
                        # Fallback to original logic
                        is_implied_installation = self._check_if_implied_installation(
                            battery_id, event_vin, event_date, all_events
                        )

                    if is_implied_installation:
                        # Handle as installation (similar to above)

                        # CRITICAL FIX: Check if there's already an active period for this battery-VIN pair
                        if (
                            event_vin in active_periods_by_vin
                            and active_periods_by_vin[event_vin] is not None
                        ):
                            # There's already an active period for this battery in this vehicle
                            existing_period = active_periods_by_vin[event_vin]
                            existing_start = existing_period["start_date"]

                            # Compare dates: use the earlier start date
                            if existing_start is None or event_date < existing_start:
                                # New event date is earlier - validate gap coverage before updating
                                gap_start = event_date
                                gap_end = (
                                    existing_start if existing_start else datetime.now()
                                )

                                # Validate that battery was continuously in this vehicle during gap
                                if self._validate_battery_gap_coverage(
                                    battery_id, event_vin, gap_start, gap_end
                                ):
                                    logger.info(
                                        f"🔧 BATTERY START UPDATE (Implied): Updating start date for battery {battery_id} in {event_vin} "
                                        f"from {existing_start.date() if existing_start else 'None'} to earlier implied date {event_date.date()} (gap validated)"
                                    )

                                    # Apply date validation for the new earlier start date
                                    validated_start, start_adjustment = (
                                        self._find_validated_date(
                                            event_vin, event_date, "forward"
                                        )
                                    )

                                    # Update the existing period with earlier start date
                                    existing_period["start_date"] = event_date
                                    existing_period["adjusted_start_date"] = (
                                        validated_start if validated_start else None
                                    )

                                    # Update note to reflect the earlier start date
                                    update_note = f"Start date updated to earlier implied installation: {event_date.date()}"
                                    if start_adjustment > 0:
                                        update_note += f" | Revalidated: start_adjusted_+{start_adjustment}d"

                                    existing_note = existing_period.get("note", "")
                                    existing_period["note"] = (
                                        f"{existing_note} | {update_note}".strip(" |")
                                    )

                                    # Update duration if there's an end date
                                    if existing_period["end_date"]:
                                        existing_period["duration_days"] = (
                                            existing_period["end_date"] - event_date
                                        ).days

                                    if existing_period[
                                        "adjusted_start_date"
                                    ] and existing_period.get("adjusted_end_date"):
                                        existing_period["adjusted_duration_days"] = (
                                            existing_period["adjusted_end_date"]
                                            - existing_period["adjusted_start_date"]
                                        ).days

                                    logger.debug(
                                        f"  → Updated existing period start date in {event_vin} (implied)"
                                    )
                                else:
                                    logger.warning(
                                        f"Gap validation failed for battery {battery_id} in {event_vin}: "
                                        f"Cannot use earlier implied date {event_date.date()} due to gap issues"
                                    )
                            else:
                                # Existing start date is earlier or same - keep existing period
                                logger.debug(
                                    f"  → Keeping existing earlier start date {existing_start.date()} for battery {battery_id} in {event_vin} (implied)"
                                )

                            processed_events.add(event_id)
                            continue

                        # Create new implied installation period (no existing active period)
                        start_date = event_date
                        method = "implied_installation"
                        confidence = "medium"

                        validated_start, start_adjustment = self._find_validated_date(
                            event_vin, start_date, "forward"
                        )

                        original_start = start_date
                        adjusted_start = validated_start if validated_start else None

                        install_note = f"Implied installation: {method}"
                        if start_adjustment > 0:
                            install_note += (
                                f" | Revalidated: start_adjusted_+{start_adjustment}d"
                            )

                        new_period = self._create_period(
                            battery_id,
                            event_vin,
                            original_start,
                            None,
                            "repair_event",
                            confidence,
                            "active",
                            note=install_note,
                            adjusted_start_date=adjusted_start,
                            adjusted_end_date=None,
                        )

                        active_periods_by_vin[event_vin] = new_period
                        logger.debug(
                            f"  → Created implied installation period in {event_vin}"
                        )
                        processed_events.add(event_id)
                        continue

                    replacement_type = "removed"
                    removal_note = "Removal (ambiguous)"

                # End active period in this vehicle if exists
                if (
                    event_vin in active_periods_by_vin
                    and active_periods_by_vin[event_vin] is not None
                ):
                    active_period = active_periods_by_vin[event_vin]

                    # Apply date validation for end date
                    validated_end, end_adjustment = self._find_validated_date(
                        event_vin, event_date, "backward"
                    )

                    original_end = event_date
                    adjusted_end = validated_end if validated_end else None

                    # Update the active period
                    active_period["end_date"] = original_end
                    active_period["adjusted_end_date"] = adjusted_end
                    active_period["lifecycle_stage"] = replacement_type

                    # Update duration
                    if active_period["start_date"]:
                        active_period["duration_days"] = (
                            original_end - active_period["start_date"]
                        ).days

                    if active_period["adjusted_start_date"] and adjusted_end:
                        active_period["adjusted_duration_days"] = (
                            adjusted_end - active_period["adjusted_start_date"]
                        ).days

                    # Add removal note
                    final_removal_note = removal_note
                    if end_adjustment > 0:
                        final_removal_note += (
                            f" | Revalidated: end_adjusted_-{end_adjustment}d"
                        )

                    existing_note = active_period.get("note", "")
                    active_period["note"] = (
                        f"{existing_note} | {final_removal_note}".strip(" |")
                    )

                    # Finalize the period
                    periods.append(active_period)

                    # Update shared vehicle cache
                    if event_vin not in vehicle_period_cache:
                        vehicle_period_cache[event_vin] = []
                    vehicle_period_cache[event_vin].append(active_period)

                    active_periods_by_vin[event_vin] = None

                    logger.debug(
                        f"  → Ended period in {event_vin} due to {replacement_type}"
                    )

                else:
                    # No active period found - create orphaned removal period

                    # Try to find start date for orphaned removal
                    try:
                        start_date, method, confidence = (
                            self._find_battery_start_with_confidence(
                                battery_id, event_date, event_vin
                            )
                        )
                        logger.debug(
                            f"  → Found start date for orphaned removal: {start_date} (method: {method}, confidence: {confidence})"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error finding start date for orphaned removal of battery {battery_id} in {event_vin}: {e}"
                        )
                        start_date, method, confidence = None, "error", "low"

                    if start_date:
                        # CRITICAL FIX: Apply vehicle-level chronological validation first
                        # Get all periods for this VIN from the shared cache
                        vehicle_periods = (
                            vehicle_period_cache.get(event_vin, []) + periods
                        )

                        # ENHANCED LOGIC: For sequential battery handoffs, store handoff information
                        # Check if this removal should trigger the next battery to start
                        next_battery_event = self._find_next_battery_event_in_vehicle(
                            event_vin, event_date, all_events
                        )
                        if (
                            next_battery_event
                            and next_battery_event.get("battery_id_old")
                            and next_battery_event.get("battery_id_old") != battery_id
                        ):
                            # Next battery should start where this one ends
                            logger.info(
                                f"  → Sequential handoff: {battery_id} ends, {next_battery_event.get('battery_id_old')} should start on {event_date.date()}"
                            )
                            # Store this information for the next battery processing
                            next_battery_id = next_battery_event.get("battery_id_old")
                            if not hasattr(self, "sequential_handoffs"):
                                self.sequential_handoffs = {}
                            self.sequential_handoffs[
                                f"{next_battery_id}_{event_vin}"
                            ] = {
                                "start_date": event_date,
                                "previous_battery": battery_id,
                                "handoff_date": event_date,
                            }

                            # CRITICAL FIX: Check if the next battery was already processed and update its start date
                            self._update_existing_battery_start_date(
                                next_battery_id,
                                event_vin,
                                event_date,
                                vehicle_period_cache,
                                periods,
                            )

                        safe_start, safe_end, is_chronologically_valid = (
                            self._validate_vehicle_level_chronology(
                                battery_id,
                                event_vin,
                                start_date,
                                event_date,
                                vehicle_periods,
                            )
                        )

                        if not is_chronologically_valid:
                            logger.warning(
                                f"🚫 CHRONOLOGY CONFLICT: Cannot create orphaned period for battery {battery_id} in {event_vin} - would overlap with other batteries"
                            )
                            processed_events.add(event_id)
                            continue

                        # Use chronologically safe dates
                        original_start = safe_start
                        original_end = safe_end

                        # Apply activity-based date validation to the safe dates
                        validated_start, start_adjustment = self._find_validated_date(
                            event_vin, original_start, "forward"
                        )
                        validated_end, end_adjustment = self._find_validated_date(
                            event_vin, original_end, "backward"
                        )

                        adjusted_start = validated_start if validated_start else None
                        adjusted_end = validated_end if validated_end else None

                        # CRITICAL FIX: Ensure adjusted dates are valid if both exist
                        if (
                            adjusted_start
                            and adjusted_end
                            and adjusted_start > adjusted_end
                        ):
                            logger.warning(
                                f"Date adjustment created invalid range for battery {battery_id}: "
                                f"adjusted start {adjusted_start.date()} > adjusted end {adjusted_end.date()}. "
                                f"Clearing adjusted dates."
                            )
                            # Clear adjusted dates to prevent negative duration
                            adjusted_start = None
                            adjusted_end = None
                            start_adjustment = 0
                            end_adjustment = 0

                        # Create orphaned period with chronologically validated dates
                        adjustment_notes = []
                        if original_start != start_date:
                            days_diff = (original_start - start_date).days
                            adjustment_notes.append(
                                f"chronology_adjusted_+{days_diff}d"
                            )
                        if original_end != event_date:
                            days_diff = (event_date - original_end).days
                            adjustment_notes.append(
                                f"chronology_adjusted_-{days_diff}d"
                            )
                        if start_adjustment > 0:
                            adjustment_notes.append(
                                f"activity_adjusted_+{start_adjustment}d"
                            )
                        if end_adjustment > 0:
                            adjustment_notes.append(
                                f"activity_adjusted_-{end_adjustment}d"
                            )

                        orphan_note = f"Orphaned {replacement_type}: {method} | Vehicle chronology validated"
                        if adjustment_notes:
                            orphan_note += f" | Adjusted: {', '.join(adjustment_notes)}"

                        orphaned_period = self._create_period(
                            battery_id,
                            event_vin,
                            original_start,
                            original_end,
                            "repair_event",
                            confidence,
                            replacement_type,
                            note=orphan_note,
                            adjusted_start_date=adjusted_start,
                            adjusted_end_date=adjusted_end,
                        )

                        periods.append(orphaned_period)

                        # Update shared vehicle cache
                        if event_vin not in vehicle_period_cache:
                            vehicle_period_cache[event_vin] = []
                        vehicle_period_cache[event_vin].append(orphaned_period)

                        logger.debug(
                            f"  → Created chronologically validated orphaned period in {event_vin}"
                        )

                processed_events.add(event_id)

        # Handle any remaining active periods (currently active batteries)
        for vin, active_period in active_periods_by_vin.items():
            if active_period is not None:
                # This battery is currently active in this vehicle
                active_period["lifecycle_stage"] = "active"
                existing_note = active_period.get("note", "")
                active_period["note"] = f"{existing_note} | Currently active".strip(
                    " |"
                )
                periods.append(active_period)

                # Update shared vehicle cache
                if vin not in vehicle_period_cache:
                    vehicle_period_cache[vin] = []
                vehicle_period_cache[vin].append(active_period)

                logger.debug(f"  → Finalized currently active period in {vin}")

        # Sort periods by start date for consistency
        periods.sort(key=lambda x: x["start_date"] or datetime.min)

        # Re-enable activity validation for comprehensive analysis
        periods = self._validate_periods_with_activity(periods)

        logger.debug(f"Created {len(periods)} periods for battery {battery_id}")
        return periods

    def _handle_ambiguous_removal(self, battery_id: str, event: Dict) -> Optional[Dict]:
        """Handle ambiguous removal events inline (battery_id_old only)."""
        vin = event["vin"]
        date = event["date"]

        # Use enhanced logic to find start date
        start_date, method, confidence = self._find_battery_start_with_confidence(
            battery_id, date, vin
        )

        if start_date:
            return self._create_period(
                battery_id,
                vin,
                start_date,
                date,
                "repair_event",
                confidence,
                "removed",
                note=f"Ambiguous removal resolved: {method}",
                adjusted_start_date=None,
                adjusted_end_date=None,
            )
        else:
            # Create period with unknown start
            return self._create_period(
                battery_id,
                vin,
                None,
                date,
                "repair_event",
                "low",
                "removed",
                note="Ambiguous removal - start unknown",
                adjusted_start_date=None,
                adjusted_end_date=None,
            )

    def _fix_status_indicators(self):
        """Fix is_currently_active and status indicators for all periods."""
        for period in self.final_timeline:
            end_date = period.get("end_date")
            lifecycle_stage = period.get("lifecycle_stage", "")
            confidence = period.get("confidence", "medium")

            # Simple logic: if end_date exists and is not null/empty, then not active
            has_end_date = (
                end_date is not None
                and not pd.isna(end_date)
                and str(end_date).strip() != ""
                and str(end_date) != "NaT"
            )

            period["is_currently_active"] = not has_end_date
            period["status"] = self._determine_battery_status(
                end_date, lifecycle_stage, confidence
            )

    def _determine_active_status(self, end_date: Optional[datetime]) -> bool:
        """Safely determine if a battery period is currently active, handling NaT values."""
        try:
            if end_date is None:
                return True

            # Handle pandas NaT values
            if hasattr(end_date, "isnull") and end_date.isnull():
                return True

            if pd.isna(end_date):
                return True

            return False
        except (TypeError, AttributeError):
            # If we can't determine safely, assume active
            return True

    def _determine_battery_status(
        self, end_date: Optional[datetime], lifecycle_stage: str, confidence: str
    ) -> str:
        """Determine clear battery status for CSV readability."""
        if end_date is None or pd.isna(end_date):
            if lifecycle_stage in ["active", "active_extended", "active_validated"]:
                return "🟢 ACTIVE (ongoing)"
            else:
                return "⚠️ INCOMPLETE (missing end date)"
        else:
            # Handle pandas NaT (Not-a-Time) values which can cause arithmetic errors
            try:
                # Convert to pandas Timestamp to handle NaT properly
                if hasattr(end_date, "isnull") and end_date.isnull():
                    if lifecycle_stage in [
                        "active",
                        "active_extended",
                        "active_validated",
                    ]:
                        return "🟢 ACTIVE (ongoing)"
                    else:
                        return "⚠️ INCOMPLETE (missing end date)"

                # Ensure we have a proper datetime object
                if pd.isna(end_date):
                    if lifecycle_stage in [
                        "active",
                        "active_extended",
                        "active_validated",
                    ]:
                        return "🟢 ACTIVE (ongoing)"
                    else:
                        return "⚠️ INCOMPLETE (missing end date)"

                days_ago = (datetime.now() - end_date).days
                if days_ago <= 30:
                    return f"🔴 ENDED ({days_ago} days ago)"
                elif days_ago <= 365:
                    return f"🔴 ENDED ({days_ago} days ago)"
                else:
                    return f"🔴 ENDED ({end_date.strftime('%Y-%m-%d')})"
            except (TypeError, AttributeError) as e:
                logger.warning(
                    f"Date arithmetic error in _determine_battery_status with end_date={end_date}, type={type(end_date)}: {e}"
                )
                # Fallback to incomplete status
                if lifecycle_stage in ["active", "active_extended", "active_validated"]:
                    return "🟢 ACTIVE (ongoing)"
                else:
                    return "⚠️ INCOMPLETE (date error)"

    def _create_period(
        self,
        battery_id: str,
        vin: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        source: str,
        confidence: str,
        lifecycle_stage: str,
        note: str = "",
        adjusted_start_date: Optional[datetime] = None,
        adjusted_end_date: Optional[datetime] = None,
    ) -> Dict:
        """Create a standardized battery period with both original and adjusted dates."""
        duration_days = None
        if start_date and end_date:
            try:
                # Handle pandas NaT values that can cause arithmetic errors
                if pd.isna(start_date) or pd.isna(end_date):
                    duration_days = None
                    logger.debug(
                        f"Cannot calculate duration for battery {battery_id}: start_date={start_date}, end_date={end_date}"
                    )
                else:
                    duration_days = (end_date - start_date).days
            except (TypeError, AttributeError) as e:
                logger.warning(
                    f"Error calculating duration for battery {battery_id} in {vin}: start_date={start_date} ({type(start_date)}), end_date={end_date} ({type(end_date)}): {e}"
                )
                duration_days = None

        adjusted_duration_days = None
        if adjusted_start_date and adjusted_end_date:
            try:
                # Handle pandas NaT values that can cause arithmetic errors
                if pd.isna(adjusted_start_date) or pd.isna(adjusted_end_date):
                    adjusted_duration_days = None
                    logger.debug(
                        f"Cannot calculate adjusted duration for battery {battery_id}: adjusted_start_date={adjusted_start_date}, adjusted_end_date={adjusted_end_date}"
                    )
                else:
                    adjusted_duration_days = (
                        adjusted_end_date - adjusted_start_date
                    ).days
            except (TypeError, AttributeError) as e:
                logger.warning(
                    f"Error calculating adjusted duration for battery {battery_id} in {vin}: adjusted_start_date={adjusted_start_date} ({type(adjusted_start_date)}), adjusted_end_date={adjusted_end_date} ({type(adjusted_end_date)}): {e}"
                )
                adjusted_duration_days = None

        return {
            "battery_id": battery_id,
            "vin": vin,
            "start_date": start_date,
            "end_date": end_date,
            "adjusted_start_date": adjusted_start_date,
            "adjusted_end_date": adjusted_end_date,
            "duration_days": duration_days,
            "adjusted_duration_days": adjusted_duration_days,
            "source": source,
            "confidence": confidence,
            "lifecycle_stage": lifecycle_stage,
            "phase_created": "phase2",
            "note": note,
            "km_validated": False,
            "activity_validated": False,
            "is_currently_active": self._determine_active_status(end_date),
            "status": self._determine_battery_status(
                end_date, lifecycle_stage, confidence
            ),
        }

    def _apply_inline_backward_chaining(
        self, battery_id: str, periods: List[Dict]
    ) -> List[Dict]:
        """Apply backward chaining to fill missing start dates."""
        for period in periods:
            if period["start_date"] is None and period["end_date"]:
                start_date, method, confidence = (
                    self._find_battery_start_with_confidence(
                        battery_id, period["end_date"], period["vin"]
                    )
                )

                if start_date:
                    period["start_date"] = start_date
                    period["duration_days"] = (period["end_date"] - start_date).days
                    period["confidence"] = confidence
                    period["note"] = f"Backward chaining: {method}"

        return periods

    def _find_sequential_battery_start(
        self, battery_id: str, removal_date: datetime, vin: str, all_events: List[Dict]
    ) -> Optional[Tuple[datetime, str, str]]:
        """Find battery start date by looking for the previous battery removal in the same VIN (improved logic)."""

        # CRITICAL FIX: Only look for sequential connections within the same VIN
        # Cross-VIN sequential connections should be handled by the new chronological algorithm

        vin_events = [
            event
            for event in all_events
            if event["vin"] == vin and event["date"] < removal_date
        ]

        if vin_events:
            # Sort by date (descending - most recent first)
            vin_events.sort(key=lambda x: x["date"], reverse=True)

            # Look for the most recent previous battery removal/replacement in the SAME VIN
            for event in vin_events:
                # Check if this event represents a battery being removed/replaced
                if event.get("battery_id_old") and event.get("battery_id_new"):
                    # Clear replacement: previous battery removed, current battery could have started here
                    previous_removal_date = event["date"]
                    previous_battery = event["battery_id_old"]
                    logger.debug(
                        f"Found same-VIN sequential connection: Battery {battery_id} likely started after {previous_battery} was removed on {previous_removal_date}"
                    )
                    return (
                        previous_removal_date,
                        f"sequential_after_{previous_battery}_in_same_vin",
                        "high",
                    )
                elif event.get("battery_id_old") and not event.get("battery_id_new"):
                    # Ambiguous removal: previous battery removed, current battery could have started here
                    previous_removal_date = event["date"]
                    previous_battery = event["battery_id_old"]
                    logger.debug(
                        f"Found same-VIN sequential connection: Battery {battery_id} likely started after {previous_battery} ambiguous removal on {previous_removal_date}"
                    )

                    # Use effective_date which already handles battery_changed → created fallback
                    return (
                        previous_removal_date,  # This is already the effective_date from event["date"]
                        f"sequential_after_{previous_battery}_ambiguous_in_same_vin",
                        "medium",  # Reduced confidence for ambiguous events
                    )

        # REMOVED: Cross-VIN sequential logic that was causing phantom overlaps
        # Cross-VIN transfers are now properly handled by the chronological algorithm
        # in _build_battery_timeline_pure

        logger.debug(
            f"No sequential connection found for battery {battery_id} in VIN {vin}"
        )
        return None

    def _check_if_implied_installation(
        self, battery_id: str, vin: str, event_date: datetime, all_events: List[Dict]
    ) -> bool:
        """
        Check if a battery_id_old-only event is actually an implied installation.

        Logic:
        1. If there are later battery events for this vehicle → removal
        2. If no later events AND battery is in working snapshot → implied installation
        3. Otherwise → ambiguous removal

        Args:
            battery_id: The battery ID in question
            vin: The vehicle VIN
            event_date: The date of the battery_id_old-only event
            all_events: All events for context

        Returns:
            True if this should be treated as an implied installation
        """

        # Step 1: Check for any battery events AFTER this date in the same vehicle
        later_events = [
            event
            for event in all_events
            if event["vin"] == vin
            and event["date"] > event_date
            and (event.get("battery_id_old") or event.get("battery_id_new"))
        ]

        if later_events:
            logger.debug(
                f"Found {len(later_events)} later battery events for {vin} after {event_date.date()}"
            )
            return False  # Later events suggest this was a removal

        # Step 2: Check if battery is in working vehicles snapshot
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        current_master = vehicle_info.get("master_battery")
        current_slave = vehicle_info.get("slave_battery")

        if battery_id in [current_master, current_slave]:
            logger.debug(
                f"Battery {battery_id} found in current snapshot for {vin} - implied installation"
            )
            return True  # Battery is currently in vehicle → implied installation

        # Step 3: Check if battery appears in any working snapshot for other vehicles
        # This could indicate a transfer, so be more cautious
        battery_in_other_snapshots = False
        for other_vin, other_info in self.vehicle_info_cache.items():
            if other_vin != vin:
                other_master = other_info.get("master_battery")
                other_slave = other_info.get("slave_battery")
                if battery_id in [other_master, other_slave]:
                    battery_in_other_snapshots = True
                    logger.debug(
                        f"Battery {battery_id} found in snapshot for other vehicle {other_vin}"
                    )
                    break

        if battery_in_other_snapshots:
            logger.debug(
                f"Battery {battery_id} in other vehicle snapshot - likely transfer/removal"
            )
            return False  # Battery transferred to another vehicle

        # Default: treat as ambiguous removal if we can't determine
        logger.debug(
            f"Battery {battery_id} not found in any snapshot - ambiguous removal"
        )
        return False

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Enhanced battery start finding with activity-based validation and sequential handoffs."""

        # PRIORITY 0: Check for sequential handoff information
        if hasattr(self, "sequential_handoffs"):
            handoff_key = f"{battery_id}_{vin}"
            logger.debug(f"  → Checking for sequential handoff: {handoff_key}")
            logger.debug(
                f"  → Available handoffs: {list(self.sequential_handoffs.keys()) if self.sequential_handoffs else 'None'}"
            )

            if handoff_key in self.sequential_handoffs:
                handoff_info = self.sequential_handoffs[handoff_key]
                handoff_date = handoff_info["start_date"]
                previous_battery = handoff_info["previous_battery"]

                logger.info(
                    f"  → Found sequential handoff for {battery_id}: start on {handoff_date.date()} after {previous_battery}"
                )

                # Verify this handoff date is before our removal date
                if not before_date or handoff_date < before_date:
                    logger.info(
                        f"  → Using sequential handoff date {handoff_date.date()} from battery {previous_battery}"
                    )
                    return (
                        handoff_date,
                        f"sequential_handoff_from_{previous_battery}",
                        "high",
                    )
                else:
                    logger.warning(
                        f"  → Handoff date {handoff_date.date()} is after removal date {before_date.date() if before_date else 'None'}"
                    )
            else:
                logger.debug(
                    f"  → No sequential handoff found for {battery_id} in {vin}"
                )

        # Priority 1: Direct installation events for this specific battery-VIN combination
        # CRITICAL FIX: Find ALL installation events for this battery-VIN pair and use the earliest
        install_events_same_vin = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and e["vin"] == vin  # Same VIN
            and (not before_date or e["date"] < before_date)
        ]

        # Also check for installation events from other VINs (cross-VIN transfers)
        install_events_other_vins = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and e["vin"] != vin  # Different VIN
            and (not before_date or e["date"] < before_date)
        ]

        if install_events_same_vin:
            # Use the earliest installation date for this specific VIN
            earliest_same_vin = min(install_events_same_vin, key=lambda x: x["date"])

            # CRITICAL FIX: Check if there's an even earlier installation from another VIN
            # that could indicate the battery was transferred to this VIN
            if install_events_other_vins:
                earliest_other_vin = min(
                    install_events_other_vins, key=lambda x: x["date"]
                )

                # If there's an earlier installation in another VIN, check if the battery
                # was continuously in this VIN from that earlier date
                gap_start = earliest_other_vin["date"]
                gap_end = earliest_same_vin["date"]

                if self._validate_battery_gap_coverage(
                    battery_id, vin, gap_start, gap_end
                ):
                    logger.info(
                        f"🔧 BATTERY START FIX: Using earlier date {gap_start.date()} instead of {earliest_same_vin['date'].date()} "
                        f"for battery {battery_id} in VIN {vin} (gap validated)"
                    )
                    # Use the earlier date but note it came from cross-VIN transfer
                    if self._validate_battery_installation_with_activity(
                        vin, gap_start
                    ):
                        return gap_start, "cross_vin_transfer_validated", "high"
                    else:
                        return gap_start, "cross_vin_transfer_validated", "medium"

            # Use the installation event for this VIN
            if self._validate_battery_installation_with_activity(
                vin, earliest_same_vin["date"]
            ):
                return earliest_same_vin["date"], "installation_event", "high"
            else:
                return earliest_same_vin["date"], "installation_event", "medium"

        # If no direct installation events for this VIN, check cross-VIN installations
        if install_events_other_vins:
            earliest = min(install_events_other_vins, key=lambda x: x["date"])
            # Validate cross-VIN installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "high",
                )
            else:
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "medium",
                )

        # Priority 2: Implied installations (battery_id in old OR new, but not both)
        implied_events = [
            e
            for e in self.cleaned_events
            if (
                # Case 1: battery_id in old, new is empty (removal/confirmation)
                (e["battery_id_old"] == battery_id and not e["battery_id_new"])
                # Case 2: battery_id in new, old is empty (installation without removal)
                or (e["battery_id_new"] == battery_id and not e["battery_id_old"])
            )
            and (not before_date or e["date"] < before_date)
        ]
        if implied_events:
            earliest = min(implied_events, key=lambda x: x["date"])
            # Validate implied installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return earliest["date"], "implied_installation", "high"
            else:
                return earliest["date"], "implied_installation", "medium"

        # Priority 3: Activity-based start date (for vehicles with good activity data)
        activity_start = self._find_activity_based_start_date(vin, before_date)
        if activity_start:
            start_date, method, confidence = activity_start
            return start_date, method, confidence

        # Priority 4: Cross-VIN snapshot matching with activity validation
        if battery_id in self.global_battery_cache:
            vins_used = self.global_battery_cache[battery_id]["vins_used"]

            for other_vin in vins_used:
                if other_vin == vin:
                    continue

                other_vehicle_info = self.vehicle_info_cache.get(other_vin, {})
                other_snapshot_batteries = [
                    other_vehicle_info.get("master_battery"),
                    other_vehicle_info.get("slave_battery"),
                ]

                if battery_id in other_snapshot_batteries:
                    # Check if there are any installation events for this battery in the other VIN
                    other_vin_installations = [
                        e
                        for e in self.cleaned_events
                        if e["battery_id_new"] == battery_id
                        and e["vin"] == other_vin
                        and (not before_date or e["date"] < before_date)
                    ]

                    if other_vin_installations:
                        # Use the most recent installation event instead of snapshot date
                        latest_installation = max(
                            other_vin_installations, key=lambda x: x["date"]
                        )
                        return (
                            latest_installation["date"],
                            f"cross_vin_installation_from_{other_vin}",
                            "high",
                        )
                    else:
                        # Try activity-based start for cross-VIN scenario first
                        activity_start = self._find_activity_based_start_date(
                            other_vin, before_date
                        )
                        if activity_start:
                            start_date, method, confidence = activity_start
                            return (
                                start_date,
                                f"cross_vin_snapshot_from_{other_vin}_{method}",
                                confidence,
                            )
                        else:
                            # Fallback to erstzulassung for cross-VIN if no activity data
                            erstzulassung = other_vehicle_info.get("erstzulassung")
                            if (
                                erstzulassung
                                and not pd.isna(erstzulassung)
                                and (not before_date or erstzulassung < before_date)
                            ):
                                return (
                                    erstzulassung,
                                    f"cross_vin_snapshot_from_{other_vin}_erstzulassung",
                                    "medium",
                                )

        # Priority 5: Snapshot matching with activity validation (fallback for current VIN)
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        if battery_id in snapshot_batteries:
            # First check if there are installation events for this battery in the current VIN
            current_vin_installations = [
                e
                for e in self.cleaned_events
                if e["battery_id_new"] == battery_id
                and e["vin"] == vin
                and (not before_date or e["date"] < before_date)
            ]

            if current_vin_installations:
                # Use the most recent installation event - this is actual event data (high priority)
                latest_installation = max(
                    current_vin_installations, key=lambda x: x["date"]
                )
                if self._validate_battery_installation_with_activity(
                    vin, latest_installation["date"]
                ):
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "high",
                    )
                else:
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "medium",
                    )

            # Fallback to erstzulassung only if no installation events exist
            erstzulassung = vehicle_info.get("erstzulassung")
            # Ensure erstzulassung is not NaT before using
            if (
                erstzulassung
                and not pd.isna(erstzulassung)
                and (not before_date or erstzulassung < before_date)
            ):
                # Try activity-based start first
                activity_start = self._find_activity_based_start_date(vin, before_date)
                if activity_start:
                    start_date, method, confidence = activity_start
                    return start_date, f"snapshot_activity_{method}", confidence
                else:
                    # Check if VIN has poor activity data quality
                    if vin in self.vehicle_activity_cache:
                        activity = self.vehicle_activity_cache[vin]
                        if activity["data_quality_score"] < 0.5:  # Poor activity data
                            return (
                                erstzulassung,
                                "snapshot_erstzulassung_poor_activity",
                                "medium",
                            )

                    # Fallback to erstzulassung if no activity data
                    return erstzulassung, "snapshot_erstzulassung_no_activity", "low"

        # Priority 6: Global battery cache fallback
        if battery_id in self.global_battery_cache:
            cache_entry = self.global_battery_cache[battery_id]
            earliest_date = cache_entry["first_seen"]
            if not before_date or earliest_date < before_date:
                return earliest_date, "global_cache_earliest", "low"

        return None, "no_start_found", "low"

    def _find_activity_based_start_date(
        self, vin: str, before_date: Optional[datetime]
    ) -> Optional[Tuple[datetime, str, str]]:
        """Find the earliest date when vehicle was actually active based on KM activity data from CSV."""
        if vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Get vehicle data from pre-indexed CSV data
            if vehicle_id not in self.daily_stats_by_vehicle:
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            # Filter for dates before the specified date
            before_date_filter = (
                before_date.date() if before_date else datetime.now().date()
            )
            filtered_data = vehicle_data[
                vehicle_data["date"].dt.date <= before_date_filter
            ]

            if filtered_data.empty:
                return None

            # Find active dates with meaningful KM activity (≥2km movement)
            active_mask = (
                (filtered_data["km_end"] - filtered_data["km_start"] >= 2)
                & (filtered_data["km_end"] > filtered_data["km_start"])
                & (filtered_data["km_start"] >= 0)
                & (filtered_data["km_end"].notna())
                & (filtered_data["km_start"].notna())
            )

            active_data = filtered_data[active_mask].sort_values("date")

            if len(active_data) > 0:
                first_row = active_data.iloc[0]
                earliest_active_date = datetime.combine(
                    first_row["date"].date(), datetime.min.time()
                )
                km_start, km_end = first_row["km_start"], first_row["km_end"]
                km_diff = km_end - km_start

                # Determine confidence based on KM activity level
                if km_diff >= 10:  # Significant activity
                    method = "earliest_active_km_high"
                    confidence = "high"
                elif km_diff >= 5:  # Moderate activity
                    method = "earliest_active_km_medium"
                    confidence = "high"
                else:  # Minimal but valid activity (>=2km)
                    method = "earliest_active_km_minimal"
                    confidence = "medium"

                return earliest_active_date, method, confidence

        except Exception as e:
            logger.debug(f"Error finding activity-based start date for {vin}: {e}")

        return None

    def _find_validated_date(
        self,
        vin: str,
        target_date: datetime,
        direction: str = "forward",
        max_days: int = 14,
    ) -> Tuple[Optional[datetime], int]:
        """
        Find a date with valid activity by adjusting target_date iteratively.

        Args:
            vin: Vehicle VIN
            target_date: Initial date to validate
            direction: "forward" for start dates (increase), "backward" for end dates (decrease)
            max_days: Maximum days to adjust

        Returns:
            Tuple of (validated_date, days_adjusted)
        """
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None, 0

        # Check if target date already has valid activity
        activity_data = self._query_vehicle_activity_on_date(vin, target_date)
        if activity_data and activity_data["has_km_activity"]:
            return target_date, 0

        # Try adjusting the date iteratively
        for days in range(1, max_days + 1):
            if direction == "forward":
                # For start dates: increase by 1 day iteratively
                adjusted_date = target_date + timedelta(days=days)
            else:
                # For end dates: decrease by 1 day iteratively
                adjusted_date = target_date - timedelta(days=days)

            activity_data = self._query_vehicle_activity_on_date(vin, adjusted_date)
            if activity_data and activity_data["has_km_activity"]:
                return adjusted_date, days

        return None, 0

    def _validate_battery_installation_with_activity(
        self, vin: str, installation_date: datetime
    ) -> bool:
        """Validate if battery installation date aligns with vehicle KM activity."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return False

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Check KM activity around installation date (±7 days window)
            start_date = installation_date - timedelta(days=7)
            end_date = installation_date + timedelta(days=7)

            query = text(
                """
                SELECT COUNT(*) as active_days,
                       AVG(CASE WHEN km_end > km_start AND km_end - km_start >= 2 THEN km_end - km_start ELSE NULL END) as avg_km
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date BETWEEN :start_date AND :end_date
                AND km_end - km_start >= 2 
                AND km_end > km_start 
                AND km_start >= 0
            """
            )

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    query,
                    {
                        "vehicle_id": vehicle_id,
                        "start_date": start_date.date(),
                        "end_date": end_date.date(),
                    },
                )
                row = result.fetchone()

                if row and row[0] > 0:  # At least some active days
                    active_days = row[0]
                    avg_km = row[1] or 0

                    # Validate meaningful KM activity
                    return active_days >= 2 and avg_km >= 2

        except Exception as e:
            logger.debug(
                f"Error validating installation for {vin} on {installation_date}: {e}"
            )

        return False

    def _validate_periods_with_activity(self, periods: List[Dict]) -> List[Dict]:
        """Validate periods against vehicle activity data."""
        for period in periods:
            vin = period["vin"]
            # Re-enable activity validation for comprehensive analysis
            activity = self._query_vehicle_activity_on_fly(vin, "summary")
            if activity:  # Re-enabled for comprehensive validation
                try:
                    validation_result = self._validate_period_activity(period)
                    period["activity_validated"] = validation_result["is_valid"]
                    period["km_validated"] = validation_result["km_valid"]

                    if validation_result["issues"]:
                        existing_note = period.get("note", "")
                        issues_note = (
                            f"Activity issues: {', '.join(validation_result['issues'])}"
                        )
                        period["note"] = f"{existing_note} | {issues_note}".strip(" |")

                except Exception as e:
                    logger.debug(f"Error validating period activity: {e}")

        return periods

    def _validate_period_activity(self, period: Dict) -> Dict:
        """Validate a single period against KM activity data from CSV."""
        vin = period["vin"]
        start_date = period["start_date"]
        end_date = period["end_date"]

        if not start_date or vin not in self.vin_to_vehicle_id:
            return {
                "is_valid": False,
                "km_valid": False,
                "issues": ["no_validation_data"],
            }

        vehicle_id = self.vin_to_vehicle_id[vin]

        try:
            # Get vehicle data from pre-indexed CSV data
            if vehicle_id not in self.daily_stats_by_vehicle:
                return {
                    "is_valid": False,
                    "km_valid": False,
                    "issues": ["no_activity_data"],
                }

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            # Filter for the period dates
            end_date_query = end_date.date() if end_date else datetime.now().date()
            period_data = vehicle_data[
                (vehicle_data["date"].dt.date >= start_date.date())
                & (vehicle_data["date"].dt.date <= end_date_query)
            ]

            if period_data.empty:
                return {
                    "is_valid": False,
                    "km_valid": False,
                    "issues": ["no_activity_data"],
                }

            total_days = len(period_data)

            # Calculate meaningful KM days (≥2km movement)
            meaningful_km_mask = (
                (period_data["km_end"] > period_data["km_start"])
                & (period_data["km_end"] - period_data["km_start"] >= 2)
                & (period_data["km_end"] >= 0)
                & (period_data["km_start"] >= 0)
                & (period_data["km_end"].notna())
                & (period_data["km_start"].notna())
            )

            meaningful_km_data = period_data[meaningful_km_mask]
            meaningful_km_days = len(meaningful_km_data)

            # Calculate average meaningful KM
            if meaningful_km_days > 0:
                avg_meaningful_km = (
                    meaningful_km_data["km_end"] - meaningful_km_data["km_start"]
                ).mean()
            else:
                avg_meaningful_km = None

            # Calculate invalid KM days
            invalid_km_mask = (
                (period_data["km_end"] <= period_data["km_start"])
                | (period_data["km_end"] < 0)
                | (period_data["km_start"] < 0)
                | (period_data["km_end"].isna())
                | (period_data["km_start"].isna())
            )
            invalid_km_days = invalid_km_mask.sum()

            issues = []
            km_valid = True

            # Enhanced validation with meaningful KM activity thresholds
            if total_days > 7:  # Only check for periods longer than a week
                meaningful_km_ratio = (
                    meaningful_km_days / total_days if total_days > 0 else 0
                )

                if meaningful_km_ratio < 0.1:  # Less than 10% meaningful KM activity
                    issues.append("low_meaningful_km_activity")
                    km_valid = False

            # Validate KM patterns with enhanced thresholds
            if meaningful_km_days > 0:
                if (
                    avg_meaningful_km and avg_meaningful_km > self.avg_km_per_day * 3
                ):  # 60km/day
                    issues.append("unusually_high_km")
                elif (
                    avg_meaningful_km and avg_meaningful_km < 2
                ):  # Below meaningful threshold
                    issues.append("unusually_low_meaningful_km")
            else:
                if total_days > 30:  # Only flag if period is longer than a month
                    issues.append("no_meaningful_km_activity")
                    km_valid = False

            # Check KM data quality
            if total_days > 0:
                invalid_km_ratio = invalid_km_days / total_days

                if invalid_km_ratio > 0.5:  # More than 50% invalid km data
                    issues.append("high_invalid_km_data")
                    km_valid = False

            is_valid = len(issues) == 0

            return {
                "is_valid": is_valid,
                "km_valid": km_valid,
                "issues": issues,
                "total_days": total_days,
                "meaningful_km_days": meaningful_km_days,
                "avg_meaningful_km": avg_meaningful_km,
                "invalid_km_days": invalid_km_days,
            }

        except Exception as e:
            logger.debug(f"Error in enhanced period validation: {e}")
            return {
                "is_valid": False,
                "km_valid": False,
                "issues": ["validation_error"],
            }

    def _validate_battery_start_with_activity(
        self, vin: str, start_date: datetime
    ) -> bool:
        """Validate if battery start date aligns with vehicle activity."""
        activity = self._query_vehicle_activity_on_fly(vin, "summary")
        if not activity:
            return False

        # Check if start date is within reasonable range of first activity
        if activity["first_activity_date"] and not pd.isna(
            activity["first_activity_date"]
        ):
            try:
                days_diff = abs(
                    (start_date.date() - activity["first_activity_date"]).days
                )
                return days_diff <= 30  # Within 30 days of first activity
            except (TypeError, AttributeError) as e:
                logger.debug(f"Error calculating date difference for {vin}: {e}")
                return False

        return False

    def _add_snapshot_only_batteries(self, processed_batteries: Set[str]) -> List[Dict]:
        """Add batteries that only appear in snapshots."""
        snapshot_periods = []

        for vin, vehicle_info in self.vehicle_info_cache.items():
            snapshot_batteries = [
                vehicle_info.get("master_battery"),
                vehicle_info.get("slave_battery"),
            ]

            for battery_id in snapshot_batteries:
                if (
                    battery_id
                    and pd.notna(battery_id)
                    and battery_id not in processed_batteries
                    and (
                        not self.test_batteries or battery_id in self.test_batteries
                    )  # Filter for test batteries
                ):

                    erstzulassung = vehicle_info.get("erstzulassung")
                    if erstzulassung:
                        period = self._create_period(
                            battery_id,
                            vin,
                            erstzulassung,
                            None,
                            "snapshot_only",
                            "medium",
                            "active",
                            note="Snapshot-only battery - no repair events found",
                            adjusted_start_date=None,
                            adjusted_end_date=None,
                        )

                        # Re-enable activity validation for comprehensive analysis
                        if self._validate_battery_start_with_activity(
                            vin, erstzulassung
                        ):
                            period["confidence"] = "high"
                            period["activity_validated"] = True

                        snapshot_periods.append(period)
                        processed_batteries.add(battery_id)

        return snapshot_periods

    # =================================================================
    # PHASE 3: COMPREHENSIVE VALIDATION AND QUALITY ASSURANCE
    # =================================================================

    def phase3_comprehensive_validation(self) -> Dict:
        """Phase 3: Final validation, deduplication, and quality assurance."""
        logger.info("=== PHASE 3: COMPREHENSIVE VALIDATION AND QUALITY ASSURANCE ===")

        # Start with timeline from Phase 2
        self.final_timeline = self.battery_timelines.copy()

        # Apply comprehensive validation
        dedup_stats = self._perform_deduplication()
        conflict_stats = self._resolve_timeline_conflicts()
        activity_stats = self._enhance_with_activity_validation()
        lifecycle_stats = self._finalize_lifecycle_stages()
        quality_metrics = self._calculate_final_quality_metrics()

        self.quality_stats = {
            "duplicates_removed": dedup_stats,
            "conflicts_resolved": conflict_stats,
            "activity_validated_periods": activity_stats,
            "lifecycle_stages_assigned": lifecycle_stats,
            "final_timeline_size": len(self.final_timeline),
            "overall_quality_score": quality_metrics["overall_score"],
            "quality_metrics": quality_metrics,
        }

        logger.info(f"Phase 3 complete: {self.quality_stats}")
        return self.quality_stats

    def _perform_deduplication(self) -> int:
        """Remove exact duplicates and merge compatible periods."""
        logger.info("Performing deduplication...")

        original_count = len(self.final_timeline)

        # Convert to DataFrame for easier deduplication
        df = pd.DataFrame(self.final_timeline)

        # Remove exact duplicates
        df = df.drop_duplicates(
            subset=["battery_id", "vin", "start_date", "end_date"], keep="first"
        )

        # Convert back to list
        self.final_timeline = df.to_dict("records")

        duplicates_removed = original_count - len(self.final_timeline)
        logger.info(f"Removed {duplicates_removed} duplicates")
        return duplicates_removed

    def _resolve_timeline_conflicts(self) -> int:
        """Resolve overlapping periods and conflicts."""
        logger.info("Resolving timeline conflicts...")

        conflicts_resolved = 0

        # Group periods by battery_id
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        # Resolve conflicts within each battery
        clean_periods = []
        for battery_id, periods in battery_groups.items():
            if len(periods) <= 1:
                clean_periods.extend(periods)
                continue

            # Sort by start date
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            # Check for overlaps and resolve
            i = 0
            while i < len(periods):
                current = periods[i]
                overlapping = [current]

                # Find overlapping periods
                j = i + 1
                while j < len(periods):
                    if self._periods_overlap(current, periods[j]):
                        overlapping.append(periods[j])
                        periods.pop(j)
                    else:
                        j += 1

                if len(overlapping) > 1:
                    # Resolve by keeping highest confidence period
                    best_period = max(
                        overlapping,
                        key=lambda x: self._confidence_score(x["confidence"]),
                    )
                    clean_periods.append(best_period)
                    conflicts_resolved += len(overlapping) - 1
                else:
                    clean_periods.extend(overlapping)

                i += 1

        self.final_timeline = clean_periods
        logger.info(f"Resolved {conflicts_resolved} conflicts")
        return conflicts_resolved

    def _enhance_with_activity_validation(self) -> int:
        """Enhance periods with comprehensive activity validation."""
        logger.info("Enhancing with activity validation...")

        validated_periods = 0

        for period in self.final_timeline:
            if not period.get("activity_validated", False):
                # Apply validation if not already done
                validated_periods += 1
                # Validation logic already applied in Phase 2, just counting here

        logger.info(f"Validated {validated_periods} periods with activity data")
        return validated_periods

    def _finalize_lifecycle_stages(self) -> int:
        """Finalize lifecycle stages with activity-based validation."""
        logger.info("Finalizing lifecycle stages...")

        stages_assigned = 0

        # Group periods by battery for lifecycle analysis
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        for battery_id, periods in battery_groups.items():
            # Sort periods chronologically
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            for i, period in enumerate(periods):
                # Enhanced lifecycle stage determination
                if i == 0:
                    # First period - check if it's near vehicle rollout
                    vehicle_info = self.vehicle_info_cache.get(period["vin"], {})
                    erstzulassung = vehicle_info.get("erstzulassung")

                    if (
                        period["start_date"]
                        and erstzulassung
                        and abs((period["start_date"] - erstzulassung).days) <= 30
                    ):
                        period["lifecycle_stage"] = "new"
                    else:
                        period["lifecycle_stage"] = "transferred"

                elif pd.isna(period["end_date"]) or period["end_date"] is None:
                    # Currently active
                    period["lifecycle_stage"] = "active"

                    # Validate with current activity
                    if period.get("activity_validated"):
                        period["lifecycle_stage"] = "active_validated"

                elif i < len(periods) - 1:
                    # Intermediate period
                    next_period = periods[i + 1]
                    if period["vin"] != next_period["vin"]:
                        period["lifecycle_stage"] = "transferred"
                    else:
                        period["lifecycle_stage"] = "replaced"

                else:
                    # Last period for this battery
                    period["lifecycle_stage"] = "removed"

                stages_assigned += 1

        logger.info(f"Assigned lifecycle stages to {stages_assigned} periods")
        return stages_assigned

    def _calculate_final_quality_metrics(self) -> Dict:
        """Calculate comprehensive quality metrics."""
        if not self.final_timeline:
            return {"overall_score": 0.0}

        total_periods = len(self.final_timeline)

        # Confidence distribution
        confidence_dist = {"high": 0, "medium": 0, "low": 0}
        for period in self.final_timeline:
            confidence_dist[period["confidence"]] += 1

        # Source distribution
        source_dist = {"repair_event": 0, "snapshot_only": 0}
        for period in self.final_timeline:
            source = period["source"]
            if source in source_dist:
                source_dist[source] += 1

        # Validation status
        activity_validated = len(
            [p for p in self.final_timeline if p.get("activity_validated")]
        )
        km_validated = len([p for p in self.final_timeline if p.get("km_validated")])

        # Completeness
        complete_periods = len(
            [p for p in self.final_timeline if p["start_date"] and p["end_date"]]
        )
        active_periods = len(
            [p for p in self.final_timeline if p["start_date"] and not p["end_date"]]
        )

        # Calculate overall quality score
        confidence_score = (
            confidence_dist["high"] * 1.0
            + confidence_dist["medium"] * 0.7
            + confidence_dist["low"] * 0.3
        ) / total_periods

        completeness_score = (complete_periods + active_periods * 0.8) / total_periods
        validation_score = (activity_validated + km_validated) / (total_periods * 2)

        overall_score = (
            confidence_score * 0.4 + completeness_score * 0.3 + validation_score * 0.3
        )

        return {
            "overall_score": round(overall_score, 3),
            "confidence_distribution": confidence_dist,
            "source_distribution": source_dist,
            "validation_status": {
                "activity_validated": activity_validated,
                "km_validated": km_validated,
            },
            "completeness": {
                "complete": complete_periods,
                "active": active_periods,
                "incomplete": total_periods - complete_periods - active_periods,
            },
        }

    # Helper methods
    def _periods_overlap(self, period1: Dict, period2: Dict) -> bool:
        """Check if two periods overlap in time."""
        start1 = period1["start_date"] or datetime.min
        end1 = period1["end_date"] or datetime.max
        start2 = period2["start_date"] or datetime.min
        end2 = period2["end_date"] or datetime.max

        return start1 < end2 and start2 < end1

    def _confidence_score(self, confidence: str) -> int:
        """Convert confidence level to numeric score."""
        scores = {"high": 3, "medium": 2, "low": 1}
        return scores.get(confidence, 0)

    def check_database_connectivity(self) -> bool:
        """Check if database connection is available for VIN to vehicle_id mapping (daily_stats now loaded from CSV)."""
        logger.info("Checking database connectivity for VIN mapping...")

        try:
            # Initialize database connection with timeout
            connection_string = self.db_connection_string + "?connect_timeout=10"
            self.db_engine = create_engine(
                connection_string, pool_timeout=10, pool_recycle=300
            )

            # Test basic connection first
            with self.db_engine.connect() as conn:
                # Set statement timeout to prevent hanging queries
                conn.execute(text("SET statement_timeout = '10s'"))

                # Simple connectivity test
                result = conn.execute(text("SELECT 1 as test"))
                test_result = result.scalar()
                if test_result != 1:
                    raise Exception("Basic connectivity test failed")

                logger.info("✅ Basic database connection verified")

                # Check vehicles table (required for VIN to vehicle_id mapping)
                try:
                    result = conn.execute(
                        text("SELECT COUNT(*) FROM public.vehicles LIMIT 1")
                    )
                    vehicles_count = result.scalar()
                    logger.info(f"✅ Vehicles table accessible for VIN mapping")
                except Exception as e:
                    logger.error(f"❌ Vehicles table check failed: {e}")
                    logger.error(
                        "Vehicles table is required for VIN to vehicle_id mapping"
                    )
                    raise Exception(f"Required vehicles table not accessible: {e}")

                logger.info(f"✅ Database connectivity verification complete")
                logger.info(f"   - Connection timeout: 10 seconds")
                logger.info(f"   - Statement timeout: 10 seconds")
                logger.info(f"   - Daily stats: Loading from CSV file")
                logger.info(f"   - VIN mapping: Loading from database")

                return True

        except Exception as e:
            logger.error(f"❌ Database connectivity check failed: {e}")
            self.db_engine = None
            return False

    # =================================================================
    # MAIN PIPELINE ORCHESTRATION
    # =================================================================

    def run_streamlined_analysis(self) -> Dict:
        """Run the complete 5-phase battery timeline analysis."""
        logger.info("Starting 5-phase streamlined battery timeline analysis...")

        # CRITICAL: Check database connectivity before proceeding
        logger.info("=" * 60)
        logger.info("PRE-FLIGHT CHECK: Database Connectivity")
        logger.info("=" * 60)

        if not self.check_database_connectivity():
            error_msg = (
                "Pipeline requires PostgreSQL database connection for VIN to vehicle_id mapping. "
                "Daily stats are loaded from CSV for performance. "
                "Please ensure database server is running and accessible."
            )
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "db_connection_string": self.db_connection_string,
            }

        logger.info("✅ Database connectivity verified - proceeding with analysis")
        logger.info("")

        start_time = datetime.now()

        try:
            # Phase 1: Data Loading and Cleaning
            phase1_stats = self.phase1_load_and_clean_data()

            # Phase 2: Unified Timeline Building
            phase2_stats = self.phase2_unified_timeline_building()

            # Phase 3: Comprehensive Validation
            phase3_stats = self.phase3_comprehensive_validation()

            # Phase 4: Vehicle Activity Validation and Timeline Extension
            phase4_stats = self.phase4_vehicle_activity_validation()

            # Phase 5: Final Validation and Quality Assurance
            phase5_stats = self.phase5_final_validation()

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Store phase stats for reporting
            self.phase_stats = {
                "phase1": phase1_stats,
                "phase2": phase2_stats,
                "phase3": phase3_stats,
                "phase4": phase4_stats,
                "phase5": phase5_stats,
            }

            # Compile final results
            results = {
                "success": True,
                "processing_time_seconds": processing_time,
                "final_timeline": self.final_timeline,
                "phase_stats": self.phase_stats,
                "summary": {
                    "total_periods": len(self.final_timeline),
                    "unique_batteries": len(
                        set(p["battery_id"] for p in self.final_timeline)
                    ),
                    "unique_vehicles": len(set(p["vin"] for p in self.final_timeline)),
                    "overall_quality_score": self.quality_stats[
                        "overall_quality_score"
                    ],
                    "activity_validated_periods": self.quality_stats[
                        "activity_validated_periods"
                    ],
                    "validation_passed": phase5_stats.get("validation_passed", False),
                    "critical_errors": phase5_stats.get("critical_errors", 0),
                },
            }

            logger.info("5-phase streamlined analysis completed successfully!")
            logger.info(f"Processing time: {processing_time:.2f} seconds")
            logger.info(f"Final timeline: {len(self.final_timeline)} periods")
            logger.info(f"Quality score: {self.quality_stats['overall_quality_score']}")
            logger.info(
                f"Activity validated: {self.quality_stats['activity_validated_periods']} periods"
            )
            logger.info(
                f"Final validation: {'✅ PASSED' if phase5_stats.get('validation_passed') else '❌ FAILED'}"
            )

            return results

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {"success": False, "error": str(e)}

    # =================================================================
    # EXPORT AND REPORTING METHODS
    # =================================================================

    def export_timeline(self, output_file: str = "battery_timeline.csv") -> str:
        """Export final timeline to CSV."""
        if not self.final_timeline:
            logger.warning("No timeline to export. Run analysis first.")
            return ""

        df = pd.DataFrame(self.final_timeline)
        df.to_csv(output_file, index=False)
        logger.info(f"Exported timeline to: {output_file}")
        return output_file

    def generate_summary_report(self) -> str:
        """Generate comprehensive summary report for 3-phase pipeline."""
        if not self.final_timeline:
            return "No timeline data available. Run analysis first."

        total_periods = len(self.final_timeline)
        unique_batteries = len(set(p["battery_id"] for p in self.final_timeline))
        unique_vehicles = len(set(p["vin"] for p in self.final_timeline))

        phase_stats = getattr(self, "phase_stats", {})
        quality_stats = getattr(self, "quality_stats", {})

        def format_number(value, fallback="N/A"):
            if isinstance(value, (int, float)) and value != "N/A":
                return f"{value:,}"
            return fallback

        # Calculate additional metrics
        activity_validated = (
            quality_stats.get("quality_metrics", {})
            .get("validation_status", {})
            .get("activity_validated", 0)
        )
        km_validated = (
            quality_stats.get("quality_metrics", {})
            .get("validation_status", {})
            .get("km_validated", 0)
        )

        report = f"""
STREAMLINED BATTERY TIMELINE ANALYSIS REPORT (4-PHASE)
{'='*70}

OVERVIEW:
- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Total Timeline Periods: {total_periods:,}
- Unique Batteries: {unique_batteries:,}
- Unique Vehicles: {unique_vehicles:,}
- Overall Quality Score: {quality_stats.get('overall_quality_score', 'N/A')}
- Activity Validated Periods: {format_number(activity_validated)}
- Km Validated Periods: {format_number(km_validated)}

STREAMLINED PROCESSING RESULTS:

Phase 1 - Data Loading & Cleaning:
- Raw Repair Records: {format_number(phase_stats.get('phase1', {}).get('raw_repair_records'))}
- Clean Events: {format_number(phase_stats.get('phase1', {}).get('clean_events'))}
- VINs with Activity Mapping: {format_number(phase_stats.get('phase1', {}).get('vehicles_with_activity'))}
- Global Battery Cache: {format_number(phase_stats.get('phase1', {}).get('global_batteries'))}
- Data Quality Score: {phase_stats.get('phase1', {}).get('data_quality_score', 'N/A')}

Phase 2 - Unified Timeline Building:
- VINs Processed: {format_number(phase_stats.get('phase2', {}).get('vins_processed'))}
- Batteries Processed: {format_number(phase_stats.get('phase2', {}).get('batteries_processed'))}
- Periods Created: {format_number(phase_stats.get('phase2', {}).get('periods_created'))}
- Edge Cases Handled: {format_number(phase_stats.get('phase2', {}).get('edge_cases_handled'))}

Phase 3 - Comprehensive Validation:
- Duplicates Removed: {format_number(phase_stats.get('phase3', {}).get('duplicates_removed'))}
- Conflicts Resolved: {format_number(phase_stats.get('phase3', {}).get('conflicts_resolved'))}
- Lifecycle Stages Assigned: {format_number(phase_stats.get('phase3', {}).get('lifecycle_stages_assigned'))}
- Final Quality Score: {quality_stats.get('overall_quality_score', 'N/A')}

Phase 4 - Vehicle Activity Validation and Timeline Extension:
- Vehicles Validated: {format_number(phase_stats.get('phase4', {}).get('vehicles_validated'))}
- Active Vehicles Without Batteries: {format_number(phase_stats.get('phase4', {}).get('active_vehicles_without_batteries'))}
- Timeline Extensions Created: {format_number(phase_stats.get('phase4', {}).get('timeline_extensions_created'))}
- Battery Gaps Identified: {format_number(phase_stats.get('phase4', {}).get('battery_gaps_identified'))}
- Cross-Vehicle Transfers Detected: {format_number(phase_stats.get('phase4', {}).get('cross_vehicle_transfers_detected'))}
- Start Date Gaps Identified: {format_number(phase_stats.get('phase4', {}).get('start_date_gaps_identified'))}
- Early Battery Assignments Created: {format_number(phase_stats.get('phase4', {}).get('early_battery_assignments_created'))}

Phase 5 - Final Validation and Quality Assurance:
- Validation Status: {'✅ PASSED' if phase_stats.get('phase5', {}).get('validation_passed') else '❌ FAILED'}
- Critical Errors: {format_number(phase_stats.get('phase5', {}).get('critical_errors'))}
- Warnings: {format_number(phase_stats.get('phase5', {}).get('warnings'))}
- Battery Uniqueness Violations: {format_number(phase_stats.get('phase5', {}).get('battery_uniqueness_violations'))}
- Period Overlaps: {format_number(phase_stats.get('phase5', {}).get('overlap_violations'))}
- Chronological Errors: {format_number(phase_stats.get('phase5', {}).get('chronological_errors'))}
- Transfer Logic Errors: {format_number(phase_stats.get('phase5', {}).get('transfer_logic_errors'))}

CONFIDENCE DISTRIBUTION:
- High Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('high'))} periods
- Medium Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('medium'))} periods  
- Low Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('low'))} periods

VALIDATION STATUS:
- Activity Validated: {format_number(activity_validated)} periods
- Km Validated: {format_number(km_validated)} periods
- PostgreSQL Integration: {'✅ Active' if self.db_engine else '❌ Disabled'}

STREAMLINED APPROACH BENEFITS:
✓ Battery-centric processing with descending date sorting
✓ Inline edge case handling reduces processing loops  
✓ PostgreSQL integration for vehicle activity validation
✓ On-demand activity data loading with caching (memory efficient)
✓ Comprehensive validation rules for km data only
✓ Single-pass timeline building with immediate confidence scoring
✓ Enhanced gap inference using fleet patterns

Fleet Validation Parameters:
- Average km/day: {self.avg_km_per_day} km
- Average SOC usage/day: {self.avg_soc_usage_per_day}%
- Validation Rules: km_end > km_start ≥ 0, 0 ≤ SOC ≤ 100

The streamlined 3-phase approach provides efficient processing while maintaining
comprehensive validation and quality assurance through activity data integration.
"""

        return report

    def export_results(
        self, output_dir: str = "battery_timeline_results"
    ) -> Dict[str, str]:
        """Export all results including enhanced statistics."""
        import os

        os.makedirs(output_dir, exist_ok=True)
        export_files = {}

        # Export final timeline with corrected status indicators
        timeline_file = os.path.join(output_dir, "battery_timeline.csv")
        self._fix_status_indicators()  # Fix status indicators before export
        self.export_timeline(timeline_file)
        export_files["timeline"] = timeline_file

        # Export summary report
        report = self.generate_summary_report()
        report_file = os.path.join(output_dir, "battery_timeline_report.txt")
        with open(report_file, "w") as f:
            f.write(report)
        export_files["report"] = report_file

        # Export phase statistics
        if hasattr(self, "phase_stats"):
            phase_stats_file = os.path.join(output_dir, "phase_statistics.csv")
            phase_data = []

            for phase_name, stats in self.phase_stats.items():
                for metric, value in stats.items():
                    phase_data.append(
                        {"phase": phase_name, "metric": metric, "value": value}
                    )

            if phase_data:
                pd.DataFrame(phase_data).to_csv(phase_stats_file, index=False)
                export_files["phase_stats"] = phase_stats_file

        # Export validation statistics (only for cached activity data)
        if self.vehicle_activity_cache:
            validation_file = os.path.join(output_dir, "activity_validation.csv")
            validation_data = []

            for vin, activity in self.vehicle_activity_cache.items():
                if activity:  # Only include non-None cached entries
                    validation_data.append(
                        {
                            "vin": vin,
                            "vehicle_id": activity["vehicle_id"],
                            "total_days": activity["total_days"],
                            "days_with_km": activity["days_with_km"],
                            "avg_daily_km": activity["avg_daily_km"],
                            "data_quality_score": activity["data_quality_score"],
                            "invalid_km_records": activity["invalid_km_records"],
                        }
                    )

            if validation_data:
                pd.DataFrame(validation_data).to_csv(validation_file, index=False)
                export_files["validation"] = validation_file

        # Export Phase 4 rejected assignments (NEW)
        if (
            hasattr(self, "phase4_rejected_assignments")
            and self.phase4_rejected_assignments
        ):
            rejected_file = os.path.join(output_dir, "phase4_rejected_assignments.csv")
            pd.DataFrame(self.phase4_rejected_assignments).to_csv(
                rejected_file, index=False
            )
            export_files["phase4_rejected"] = rejected_file
            logger.info(
                f"Exported {len(self.phase4_rejected_assignments)} Phase 4 rejected assignments to: {rejected_file}"
            )

        # Export Phase 4 overlap conflicts (NEW)
        if hasattr(self, "phase4_overlap_conflicts") and self.phase4_overlap_conflicts:
            conflicts_file = os.path.join(output_dir, "phase4_overlap_conflicts.txt")
            with open(conflicts_file, "w") as f:
                f.write("Phase 4 Battery Assignment Overlap Conflicts\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Total conflicts: {len(self.phase4_overlap_conflicts)}\n")
                f.write(
                    f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                )

                for i, conflict in enumerate(self.phase4_overlap_conflicts, 1):
                    f.write(f"Conflict #{i}:\n")
                    f.write(f"  VIN: {conflict['vin']}\n")
                    f.write(f"  Battery ID: {conflict['battery_id']}\n")
                    f.write(f"  Proposed Start: {conflict['proposed_start']}\n")
                    f.write(f"  Proposed End: {conflict['proposed_end']}\n")
                    f.write(f"  Reason: {conflict['reason']}\n")
                    if "conflict_details" in conflict and conflict["conflict_details"]:
                        details = conflict["conflict_details"]
                        f.write(
                            f"  Conflict Details: {details.get('message', 'No details')}\n"
                        )
                    f.write("\n")

            export_files["phase4_conflicts"] = conflicts_file
            logger.info(
                f"Exported {len(self.phase4_overlap_conflicts)} Phase 4 overlap conflicts to: {conflicts_file}"
            )

        logger.info(f"Exported results to: {output_dir}")
        return export_files

    # Legacy compatibility
    def run_analysis(self) -> Dict:
        """Legacy compatibility method - runs streamlined analysis."""
        return self.run_streamlined_analysis()

    @staticmethod
    def print_database_connection_guide():
        """Print guidance for database connection setup (hybrid CSV + PostgreSQL mode)."""
        print("\n" + "=" * 70)
        print("HYBRID DATABASE CONNECTION REQUIREMENTS")
        print("=" * 70)
        print("The pipeline uses a hybrid approach:")
        print("  • Daily stats: Loaded from CSV file for performance")
        print("  • VIN mapping: Loaded from PostgreSQL for accuracy")
        print("\nRequired Environment Variables:")
        print("  DB_HOST     (default: localhost)")
        print("  DB_PORT     (default: 6543)")
        print("  DB_NAME     (default: LeitwartenDB)")
        print("  DB_USER     (default: datadump)")
        print("  DB_PASSWORD (default: pAUjuLftyHURa5Ra)")
        print("\nRequired Data Sources:")
        print(
            "  ✓ daily_stats.csv    - Vehicle activity data (vehicle_id, date, km_start, km_end)"
        )
        print("  ✓ public.vehicles    - VIN to vehicle_id mapping (PostgreSQL)")
        print("\nTroubleshooting:")
        print("  1. Ensure daily_stats.csv exists in the working directory")
        print("  2. Ensure PostgreSQL server is running")
        print("  3. Verify connection parameters")
        print("  4. Check network connectivity to database host")
        print("  5. Confirm database user has SELECT permissions on vehicles table")
        print("=" * 70)

    def run_analysis_with_db_check(self) -> Dict:
        """Run analysis with mandatory database connectivity check."""
        self.print_database_connection_guide()
        print("\nStarting pipeline with mandatory database connectivity check...")
        return self.run_streamlined_analysis()

    def run_analysis_without_db_validation(self) -> Dict:
        """Run analysis without database connectivity requirements (legacy mode)."""
        logger.warning("Running pipeline in legacy mode WITHOUT database validation")
        logger.warning("Activity validation and km validation will be disabled")

        # Set legacy mode flag and use optional database connection
        self._legacy_mode = True
        self.db_engine = None

        start_time = datetime.now()

        try:
            # Initialize database connection optionally
            self._initialize_database_connection_optional()

            # Phase 1: Data Loading and Cleaning
            phase1_stats = self.phase1_load_and_clean_data()

            # Phase 2: Unified Timeline Building
            phase2_stats = self.phase2_unified_timeline_building()

            # Phase 3: Comprehensive Validation
            phase3_stats = self.phase3_comprehensive_validation()

            # Phase 4: Vehicle Activity Validation and Timeline Extension
            phase4_stats = self.phase4_vehicle_activity_validation()

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Store phase stats for reporting
            self.phase_stats = {
                "phase1": phase1_stats,
                "phase2": phase2_stats,
                "phase3": phase3_stats,
                "phase4": phase4_stats,
            }

            # Compile final results
            results = {
                "success": True,
                "processing_time_seconds": processing_time,
                "final_timeline": self.final_timeline,
                "phase_stats": self.phase_stats,
                "database_validation_enabled": bool(self.db_engine),
                "summary": {
                    "total_periods": len(self.final_timeline),
                    "unique_batteries": len(
                        set(p["battery_id"] for p in self.final_timeline)
                    ),
                    "unique_vehicles": len(set(p["vin"] for p in self.final_timeline)),
                    "overall_quality_score": self.quality_stats[
                        "overall_quality_score"
                    ],
                    "activity_validated_periods": self.quality_stats[
                        "activity_validated_periods"
                    ],
                },
            }

            logger.info("3-phase streamlined analysis completed!")
            logger.info(f"Processing time: {processing_time:.2f} seconds")
            logger.info(f"Final timeline: {len(self.final_timeline)} periods")
            logger.info(f"Quality score: {self.quality_stats['overall_quality_score']}")
            logger.info(
                f"Database validation: {'Enabled' if self.db_engine else 'Disabled'}"
            )

            return results

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def _check_if_implied_installation(
        self, battery_id: str, vin: str, event_date: datetime, all_events: List[Dict]
    ) -> bool:
        """
        Check if a battery_id_old-only event is actually an implied installation.

        Logic:
        1. If there are later battery events for this vehicle → removal
        2. If no later events AND battery is in working snapshot → implied installation
        3. Otherwise → ambiguous removal

        Args:
            battery_id: The battery ID in question
            vin: The vehicle VIN
            event_date: The date of the battery_id_old-only event
            all_events: All events for context

        Returns:
            True if this should be treated as an implied installation
        """

        # Step 1: Check for any battery events AFTER this date in the same vehicle
        later_events = [
            event
            for event in all_events
            if event["vin"] == vin
            and event["date"] > event_date
            and (event.get("battery_id_old") or event.get("battery_id_new"))
        ]

        if later_events:
            logger.debug(
                f"Found {len(later_events)} later battery events for {vin} after {event_date.date()}"
            )
            return False  # Later events suggest this was a removal

        # Step 2: Check if battery is in working vehicles snapshot
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        current_master = vehicle_info.get("master_battery")
        current_slave = vehicle_info.get("slave_battery")

        if battery_id in [current_master, current_slave]:
            logger.debug(
                f"Battery {battery_id} found in current snapshot for {vin} - implied installation"
            )
            return True  # Battery is currently in vehicle → implied installation

        # Step 3: Check if battery appears in any working snapshot for other vehicles
        # This could indicate a transfer, so be more cautious
        battery_in_other_snapshots = False
        for other_vin, other_info in self.vehicle_info_cache.items():
            if other_vin != vin:
                other_master = other_info.get("master_battery")
                other_slave = other_info.get("slave_battery")
                if battery_id in [other_master, other_slave]:
                    battery_in_other_snapshots = True
                    logger.debug(
                        f"Battery {battery_id} found in snapshot for other vehicle {other_vin}"
                    )
                    break

        if battery_in_other_snapshots:
            logger.debug(
                f"Battery {battery_id} in other vehicle snapshot - likely transfer/removal"
            )
            return False  # Battery transferred to another vehicle

        # Default: treat as ambiguous removal if we can't determine
        logger.debug(
            f"Battery {battery_id} not found in any snapshot - ambiguous removal"
        )
        return False

    # =================================================================
    # PHASE 4: VEHICLE ACTIVITY VALIDATION AND TIMELINE EXTENSION
    # =================================================================

    def _vehicle_has_active_battery(self, vin: str) -> bool:
        """Check if a vehicle currently has any active battery (end_date is None)."""
        vehicle_periods = [p for p in self.final_timeline if p["vin"] == vin]
        active_periods = [
            p
            for p in vehicle_periods
            if p["end_date"] is None or pd.isna(p["end_date"])
        ]

        if active_periods:
            return True
        else:
            return False

    def _get_vehicle_active_batteries(self, vin: str) -> List[str]:
        """Get all currently active battery IDs for a vehicle."""
        active_batteries = []
        for period in self.final_timeline:
            if period["vin"] == vin and (
                period["end_date"] is None or pd.isna(period["end_date"])
            ):
                active_batteries.append(period["battery_id"])
        return active_batteries

    def phase4_vehicle_activity_validation(self) -> Dict:
        """Phase 4: Validate battery timelines against vehicle activity and extend missing periods."""
        logger.info(
            "=== PHASE 4: VEHICLE ACTIVITY VALIDATION AND TIMELINE EXTENSION ==="
        )

        # Initialize reject/conflict tracking
        self.phase4_rejected_assignments = []
        self.phase4_overlap_conflicts = []

        # Get all unique vehicles from timeline and daily stats
        timeline_vehicles = set(period["vin"] for period in self.final_timeline)
        activity_vehicles = set(self.vin_to_vehicle_id.keys())
        all_vehicles = timeline_vehicles.union(activity_vehicles)

        logger.info(f"Validating {len(all_vehicles)} vehicles against activity data")

        validation_stats = {
            "vehicles_validated": 0,
            "active_vehicles_without_batteries": 0,
            "timeline_extensions_created": 0,
            "battery_gaps_identified": 0,
            "cross_vehicle_transfers_detected": 0,
            "start_date_gaps_identified": 0,
            "early_battery_assignments_created": 0,
            "rejected_inactive_vehicles": 0,
            "rejected_overlap_conflicts": 0,
            "activity_threshold_days": 90,  # 3 months threshold
            "battery_extension_days": 360,  # 12 months threshold
        }

        vehicles_needing_batteries = []
        vehicles_needing_early_batteries = []
        battery_extensions = []

        # Step 1: Identify vehicles that are active but have no current battery (end gaps)
        for vin in all_vehicles:
            validation_stats["vehicles_validated"] += 1

            # Get latest activity date for this vehicle
            latest_activity_date = self._get_vehicle_latest_activity_date(vin)
            if not latest_activity_date:
                continue

            # CRITICAL FIX: Check if vehicle is actually still active (within 3 months)
            days_since_activity = (datetime.now() - latest_activity_date).days
            if days_since_activity > validation_stats["activity_threshold_days"]:
                self.phase4_rejected_assignments.append(
                    {
                        "vin": vin,
                        "latest_activity_date": latest_activity_date.date(),
                        "days_since_activity": days_since_activity,
                        "reason": f"Vehicle inactive for {days_since_activity} days (threshold: {validation_stats['activity_threshold_days']}d)",
                        "rejection_type": "inactive_vehicle",
                    }
                )
                validation_stats["rejected_inactive_vehicles"] += 1
                continue

            # Get current battery timeline for this vehicle
            vehicle_periods = [p for p in self.final_timeline if p["vin"] == vin]

            # Find if vehicle has an active battery (no end date or end date after latest activity)
            has_active_battery = False
            latest_battery_end = None

            for period in vehicle_periods:
                if period["end_date"] is None:
                    has_active_battery = True
                    break
                elif period["end_date"] > latest_activity_date:
                    has_active_battery = True
                    break
                else:
                    if (
                        latest_battery_end is None
                        or period["end_date"] > latest_battery_end
                    ):
                        latest_battery_end = period["end_date"]

            if not has_active_battery and latest_activity_date:
                validation_stats["active_vehicles_without_batteries"] += 1
                validation_stats["battery_gaps_identified"] += 1

                vehicles_needing_batteries.append(
                    {
                        "vin": vin,
                        "latest_activity_date": latest_activity_date,
                        "latest_battery_end": latest_battery_end,
                        "gap_start": (
                            latest_battery_end
                            if latest_battery_end
                            else datetime(2020, 1, 1)
                        ),
                        "vehicle_periods": vehicle_periods,
                    }
                )

        logger.info(
            f"Found {len(vehicles_needing_batteries)} active vehicles needing battery assignments"
        )

        # Step 1.5: NEW - Identify vehicles with start date gaps (active before first battery)
        for vin in all_vehicles:
            # Get earliest activity date for this vehicle
            earliest_activity_date = self._get_vehicle_earliest_activity_date(vin)
            if not earliest_activity_date:
                continue

            # Get current battery timeline for this vehicle
            vehicle_periods = [p for p in self.final_timeline if p["vin"] == vin]

            if not vehicle_periods:
                # Vehicle has activity but no battery timeline at all
                # logger.warning(
                #     f"Vehicle {vin} has activity from {earliest_activity_date.date()} but no battery timeline"
                # )
                validation_stats["start_date_gaps_identified"] += 1

                vehicles_needing_early_batteries.append(
                    {
                        "vin": vin,
                        "earliest_activity_date": earliest_activity_date,
                        "earliest_battery_start": None,
                        "gap_end": earliest_activity_date,
                        "vehicle_periods": vehicle_periods,
                    }
                )
                continue

            # Find earliest battery start date for this vehicle
            earliest_battery_start = None
            for period in vehicle_periods:
                if period["start_date"]:
                    if (
                        earliest_battery_start is None
                        or period["start_date"] < earliest_battery_start
                    ):
                        earliest_battery_start = period["start_date"]

            # Check if vehicle was active before first battery assignment
            if (
                earliest_battery_start
                and earliest_activity_date < earliest_battery_start
            ):
                gap_days = (earliest_battery_start - earliest_activity_date).days
                if gap_days > 7:  # Only consider significant gaps (more than a week)
                    logger.warning(
                        f"Vehicle {vin} was active from {earliest_activity_date.date()} but first battery starts on {earliest_battery_start.date()} (gap: {gap_days} days)"
                    )
                    validation_stats["start_date_gaps_identified"] += 1

                    vehicles_needing_early_batteries.append(
                        {
                            "vin": vin,
                            "earliest_activity_date": earliest_activity_date,
                            "earliest_battery_start": earliest_battery_start,
                            "gap_end": earliest_battery_start,
                            "vehicle_periods": vehicle_periods,
                        }
                    )

        logger.info(
            f"Found {len(vehicles_needing_early_batteries)} vehicles needing early battery assignments"
        )

        # Step 2: Try to assign batteries to vehicles with end gaps - WITH VALIDATION
        # CRITICAL FIX: Respect "one battery per vehicle" constraint AND prevent negative durations
        validation_stats["battery_conflicts_prevented"] = 0

        for vehicle_data in vehicles_needing_batteries:
            vin = vehicle_data["vin"]
            gap_start = vehicle_data["gap_start"]
            latest_activity = vehicle_data["latest_activity_date"]

            # CRITICAL FIX: Check if vehicle already has an active battery from other sources
            if self._vehicle_has_active_battery(vin):
                active_batteries = self._get_vehicle_active_batteries(vin)
                logger.info(
                    f"🔒 SKIPPING: Vehicle {vin} already has active battery(s): {active_batteries} - not assigning additional battery"
                )
                validation_stats["battery_conflicts_prevented"] += 1
                continue

            # Strategy 1: FIXED - Find any battery that ended but is not currently active anywhere
            # Create NEW period starting from that battery's last removal date - WITH VALIDATION
            available_battery = self._find_available_battery_for_active_vehicle(vin)
            if available_battery:
                battery_id = available_battery["battery_id"]
                battery_last_end = available_battery["last_end_date"]

                # CRITICAL FIX: Check timing compatibility before creating period
                if battery_last_end > latest_activity:
                    # Battery ended AFTER vehicle's last activity - this would create negative duration
                    negative_days = (battery_last_end - latest_activity).days

                    self.phase4_rejected_assignments.append(
                        {
                            "vin": vin,
                            "battery_id": battery_id,
                            "battery_last_end": battery_last_end.date(),
                            "vehicle_last_activity": latest_activity.date(),
                            "negative_duration_days": negative_days,
                            "reason": f"Battery ended after vehicle's last activity (would create negative duration: -{negative_days} days)",
                            "rejection_type": "timing_incompatible",
                        }
                    )
                    validation_stats["rejected_overlap_conflicts"] += 1
                    continue

                # CRITICAL FIX: Check for battery timeline overlaps before creating period
                overlap_conflict = self._check_battery_timeline_overlap(
                    battery_id, vin, battery_last_end, None
                )
                if overlap_conflict:

                    self.phase4_overlap_conflicts.append(
                        {
                            "vin": vin,
                            "battery_id": battery_id,
                            "proposed_start": battery_last_end.date(),
                            "proposed_end": "None (ongoing)",
                            "conflict_details": overlap_conflict,
                            "reason": "Would create battery timeline overlap",
                        }
                    )
                    validation_stats["rejected_overlap_conflicts"] += 1
                    continue

                logger.info(
                    f"🔄 STRATEGY 1 for {vin}: Creating validated NEW period for battery {battery_id} from {battery_last_end.date()} (timing compatible)"
                )

                # Create NEW period starting from battery's last removal date, ending as active (no end date)
                extension = self._create_timeline_extension(
                    battery_id,
                    vin,
                    battery_last_end,  # Start from when battery was last removed
                    None,  # Ongoing period - no end date (FIXED: no negative duration)
                    "phase4_new_period_assignment",
                    f"NEW period: Battery {battery_id} assigned to active vehicle (last removed: {battery_last_end.date()}, validated timing)",
                )
                battery_extensions.append(extension)
                validation_stats["timeline_extensions_created"] += 1
                validation_stats["cross_vehicle_transfers_detected"] += 1
                continue

            # Strategy 2: FALLBACK - Extend vehicle's own historical battery (only if orphaned assignment failed)
            # This handles cases where the vehicle's own battery period should be extended
            last_battery = self._find_last_battery_for_vehicle(vin, gap_start)
            if last_battery:
                logger.info(
                    f"🔄 STRATEGY 2 for {vin}: Extending own historical battery {last_battery['battery_id']}"
                )
                battery_id = last_battery["battery_id"]

                # ADDITIONAL CHECK: Ensure this battery is not already active elsewhere
                other_active_vehicles = [
                    p["vin"]
                    for p in self.final_timeline
                    if p["battery_id"] == battery_id
                    and p["end_date"] is None
                    and p["vin"] != vin
                ]

                if other_active_vehicles:
                    logger.debug(
                        f"Battery {battery_id} is already active in {other_active_vehicles} - cannot extend in {vin}"
                    )
                    validation_stats["battery_conflicts_prevented"] += 1
                    continue

                # Extend the existing period (traditional extension) - this should not create negative durations
                extension = self._create_timeline_extension(
                    battery_id,
                    vin,
                    gap_start,
                    latest_activity,
                    "vehicle_historical_battery_extension",
                    f"Extended own historical battery based on vehicle activity until {latest_activity.date()}",
                )
                battery_extensions.append(extension)
                validation_stats["timeline_extensions_created"] += 1

        # Step 2.5: NEW - Try to assign batteries to vehicles with start date gaps
        for vehicle_data in vehicles_needing_early_batteries:
            vin = vehicle_data["vin"]
            earliest_activity = vehicle_data["earliest_activity_date"]
            gap_end = vehicle_data["gap_end"]

            # Strategy 1: Find the earliest installed battery for this vehicle and extend backward
            earliest_battery = self._find_earliest_battery_for_vehicle(vin)
            if earliest_battery:
                extension = self._create_timeline_extension(
                    earliest_battery["battery_id"],
                    vin,
                    earliest_activity,
                    gap_end,
                    "early_vehicle_activity_extension",
                    f"Extended backward based on early vehicle activity from {earliest_activity.date()}",
                )
                battery_extensions.append(extension)
                validation_stats["early_battery_assignments_created"] += 1
                continue

            # Strategy 2: Look for batteries that started around the early activity and might have been installed
            early_battery = self._find_battery_for_early_activity(
                vin, earliest_activity, gap_end
            )
            if early_battery:
                extension = self._create_timeline_extension(
                    early_battery["battery_id"],
                    vin,
                    earliest_activity,
                    gap_end,
                    "early_activity_battery_assignment",
                    f"Battery assignment based on early activity pattern from {early_battery['source_vin']}",
                )
                battery_extensions.append(extension)
                validation_stats["early_battery_assignments_created"] += 1
                validation_stats["cross_vehicle_transfers_detected"] += 1

        # Step 3: Add extensions to final timeline
        self.final_timeline.extend(battery_extensions)

        # Step 4: Update existing periods that should be extended
        extension_updates = self._update_existing_periods_with_activity_data()
        validation_stats.update(extension_updates)

        logger.info(f"Phase 4 complete: {validation_stats}")
        return validation_stats

    def _get_vehicle_latest_activity_date(self, vin: str) -> Optional[datetime]:
        """Get the latest activity date for a vehicle from daily stats CSV."""
        if vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            # Find meaningful activity dates (≥2km movement)
            active_mask = (
                (vehicle_data["km_end"] - vehicle_data["km_start"] >= 2)
                & (vehicle_data["km_end"] > vehicle_data["km_start"])
                & (vehicle_data["km_start"] >= 0)
                & (vehicle_data["km_end"].notna())
                & (vehicle_data["km_start"].notna())
            )

            active_data = vehicle_data[active_mask]

            if len(active_data) > 0:
                latest_activity = active_data["date"].max()
                if pd.notna(latest_activity):
                    return datetime.combine(latest_activity.date(), datetime.min.time())
                else:
                    logger.debug(f"Latest activity date is NaT for {vin}")
                    return None

        except Exception as e:
            logger.debug(f"Error getting latest activity for {vin}: {e}")

        return None

    def _get_vehicle_earliest_activity_date(self, vin: str) -> Optional[datetime]:
        """Get the earliest activity date for a vehicle from daily stats CSV."""
        if vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            # Find meaningful activity dates (≥2km movement)
            active_mask = (
                (vehicle_data["km_end"] - vehicle_data["km_start"] >= 2)
                & (vehicle_data["km_end"] > vehicle_data["km_start"])
                & (vehicle_data["km_start"] >= 0)
                & (vehicle_data["km_end"].notna())
                & (vehicle_data["km_start"].notna())
            )

            active_data = vehicle_data[active_mask]

            if len(active_data) > 0:
                earliest_activity = active_data["date"].min()
                if pd.notna(earliest_activity):
                    return datetime.combine(
                        earliest_activity.date(), datetime.min.time()
                    )
                else:
                    logger.debug(f"Earliest activity date is NaT for {vin}")
                    return None

        except Exception as e:
            logger.debug(f"Error getting earliest activity for {vin}: {e}")

        return None

    def _find_last_battery_for_vehicle(
        self, vin: str, before_date: datetime
    ) -> Optional[Dict]:
        """Find the last known battery for a vehicle before a specific date."""
        vehicle_periods = [
            p
            for p in self.final_timeline
            if p["vin"] == vin
            and (p["end_date"] is None or p["end_date"] <= before_date)
        ]

        if vehicle_periods:
            # Sort by start date and return the latest
            vehicle_periods.sort(
                key=lambda x: x["start_date"] or datetime.min, reverse=True
            )
            return vehicle_periods[0]

        return None

    def _find_earliest_battery_for_vehicle(self, vin: str) -> Optional[Dict]:
        """Find the earliest known battery for a vehicle."""
        vehicle_periods = [p for p in self.final_timeline if p["vin"] == vin]

        if vehicle_periods:
            # Sort by start date and return the earliest
            vehicle_periods.sort(key=lambda x: x["start_date"] or datetime.max)
            return vehicle_periods[0]

        return None

    def _find_battery_for_early_activity(
        self, vin: str, earliest_activity: datetime, gap_end: datetime
    ) -> Optional[Dict]:
        """Find a battery that might have been in this vehicle during early activity period.

        CONSERVATIVE APPROACH: Only suggest batteries if there's strong evidence of actual transfer.
        Don't create phantom assignments based on timing proximity alone.
        """

        # CRITICAL FIX: Be much more conservative about cross-vehicle assignments
        # Only suggest batteries if there's direct evidence they belonged to this vehicle

        # Strategy 1: Check if battery appears in working vehicles snapshot for this VIN
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        # If this vehicle has a snapshot battery, check if it has any timeline
        for battery_id in snapshot_batteries:
            if battery_id and pd.notna(battery_id):
                # Check if this battery already has periods for this VIN
                battery_periods_for_vin = [
                    p
                    for p in self.final_timeline
                    if p["battery_id"] == battery_id and p["vin"] == vin
                ]

                if battery_periods_for_vin:
                    # Battery already has timeline for this VIN - use the earliest
                    earliest_period = min(
                        battery_periods_for_vin,
                        key=lambda x: x["start_date"] or datetime.max,
                    )

                    # Check if we can reasonably extend this backward based on erstzulassung
                    erstzulassung = vehicle_info.get("erstzulassung")
                    if (
                        erstzulassung
                        and not pd.isna(erstzulassung)
                        and earliest_activity >= erstzulassung
                    ):
                        gap_days = (
                            (earliest_period["start_date"] - earliest_activity).days
                            if earliest_period["start_date"]
                            else 0
                        )
                        if (
                            gap_days > 0
                            and gap_days
                            <= VALIDATION_CONSTANTS["battery_extension_days"]
                        ):  # Only extend up to 12 months backward
                            return {
                                "battery_id": battery_id,
                                "source_vin": vin,  # Same VIN, not cross-vehicle
                                "start_date": earliest_period["start_date"],
                                "activity_distance": gap_days,
                            }

        # Strategy 2: DISABLED - No cross-vehicle assignments for early activity
        # Cross-vehicle assignments based on timing proximity are fundamentally flawed
        # and create phantom assignments. A battery ending in vehicle A around
        # the time vehicle B has early activity does NOT mean the battery was in vehicle B.
        #
        # REMOVED: All cross-vehicle transfer detection logic for early activity
        # This prevents phantom assignments like battery 20400 being assigned to
        # dozens of unrelated vehicles.

        # Strategy 3: No assignment - better to have no battery than wrong battery
        # This prevents the phantom assignment problem
        logger.debug(
            f"No suitable battery found for early activity in {vin} - being conservative"
        )
        return None

    def _find_transferred_battery_for_gap(
        self, vin: str, gap_start: datetime, gap_end: datetime
    ) -> Optional[Dict]:
        """Find a battery that might have transferred to this vehicle to fill the gap.

        ENHANCED LOGIC: Prioritize orphaned batteries that ended in other vehicles but are not currently active anywhere.
        This handles cases like battery 20400 where it was removed from one vehicle but should go to an active vehicle without a current battery.
        """
        # Strategy 1: Look for orphaned batteries (ended somewhere but not currently active anywhere)
        orphaned_battery = self._find_orphaned_battery_for_active_vehicle(
            vin, gap_start
        )
        if orphaned_battery:
            return orphaned_battery

        # Strategy 2: Original logic - Look for batteries that ended around the gap start time
        candidate_batteries = []

        gap_window_start = gap_start - timedelta(days=30)  # 30-day window
        gap_window_end = gap_start + timedelta(days=30)

        for period in self.final_timeline:
            if (
                period["vin"] != vin
                and period["end_date"]
                and gap_window_start <= period["end_date"] <= gap_window_end
            ):

                # Check if this battery is not active elsewhere after gap_start
                is_active_elsewhere = False
                for other_period in self.final_timeline:
                    if (
                        other_period["battery_id"] == period["battery_id"]
                        and other_period["start_date"]
                        and other_period["start_date"] > gap_start
                    ):
                        is_active_elsewhere = True
                        break

                if not is_active_elsewhere:
                    candidate_batteries.append(
                        {
                            "battery_id": period["battery_id"],
                            "source_vin": period["vin"],
                            "end_date": period["end_date"],
                            "gap_distance": abs((period["end_date"] - gap_start).days),
                        }
                    )

        # Return the battery with the closest end date to gap start
        if candidate_batteries:
            candidate_batteries.sort(key=lambda x: x["gap_distance"])
            return candidate_batteries[0]

        return None

    def _find_available_battery_for_active_vehicle(self, vin: str) -> Optional[Dict]:
        """CORRECT: Find batteries that have historical connection to this VIN and are now available.

        For battery 20400 + WS5D16HAAJA103503:
        1. Check if 20400 was ever in WS5D16HAAJA103503 (repair events or snapshot)
        2. Check if 20400 is currently not active anywhere
        3. If both true, return 20400 with its last removal date
        """
        # Step 1: Find all batteries that have historical connection to this VIN
        historical_batteries = set()

        # From timeline periods (repair events)
        for period in self.final_timeline:
            if period["vin"] == vin:
                historical_batteries.add(period["battery_id"])

        # From working vehicle snapshots
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]
        for battery_id in snapshot_batteries:
            if battery_id and pd.notna(battery_id):
                historical_batteries.add(battery_id)

        # Step 2: Check which historical batteries are currently available (not active anywhere)
        available_historical_batteries = []

        for battery_id in historical_batteries:
            # Check if this battery is currently active anywhere
            is_currently_active = False
            last_end_date = None
            last_end_vin = None

            for period in self.final_timeline:
                if period["battery_id"] == battery_id:
                    if period["end_date"] is None:
                        is_currently_active = True
                        break
                    elif period["end_date"] and (
                        last_end_date is None or period["end_date"] > last_end_date
                    ):
                        last_end_date = period["end_date"]
                        last_end_vin = period["vin"]

            if not is_currently_active and last_end_date:
                available_historical_batteries.append(
                    {
                        "battery_id": battery_id,
                        "last_end_date": last_end_date,
                        "last_end_vin": last_end_vin,
                    }
                )
                logger.info(
                    f"   ✅ {battery_id} available (ended {last_end_date.date()} in {last_end_vin})"
                )

        if available_historical_batteries:
            # Sort by most recent end date (prefer most recently ended)
            available_historical_batteries.sort(
                key=lambda x: x["last_end_date"], reverse=True
            )
            best_battery = available_historical_batteries[0]

            return best_battery

        return None

    def _find_orphaned_battery_for_active_vehicle(
        self, vin: str, gap_start: datetime
    ) -> Optional[Dict]:
        """Find orphaned batteries that ended in other vehicles but are not currently active anywhere.

        This handles the battery 20400 case:
        - Battery ended in one vehicle but is not currently active anywhere
        - Current vehicle is active but has no battery
        - Assign the orphaned battery to the active vehicle
        """
        # Find all batteries that have ended periods but no current active periods
        ended_batteries = {}
        active_batteries = set()

        # Collect all ended periods and active periods
        for period in self.final_timeline:
            battery_id = period["battery_id"]

            if period["end_date"] is None:
                # Currently active battery
                active_batteries.add(battery_id)
            elif period["end_date"]:
                # Ended battery - track the latest end date
                if (
                    battery_id not in ended_batteries
                    or period["end_date"] > ended_batteries[battery_id]["end_date"]
                ):
                    ended_batteries[battery_id] = {
                        "battery_id": battery_id,
                        "source_vin": period["vin"],
                        "end_date": period["end_date"],
                        "last_period": period,
                    }

        # Find orphaned batteries (ended but not currently active)
        orphaned_candidates = []
        logger.info(
            f"🔍 ORPHAN CHECK for {vin}: {len(ended_batteries)} ended batteries, {len(active_batteries)} active"
        )

        for battery_id, battery_info in ended_batteries.items():
            if battery_id not in active_batteries:
                # This battery is orphaned - not currently active anywhere
                days_since_end = (datetime.now() - battery_info["end_date"]).days
                logger.debug(
                    f"   Battery {battery_id} ended {days_since_end} days ago in {battery_info['source_vin']}"
                )

                # Only consider relatively recent endings (within 2 years for active vehicles)
                if days_since_end <= 730:
                    # Check if this vehicle was the last vehicle to have this battery
                    was_in_this_vehicle = battery_info["source_vin"] == vin

                    # Prioritize batteries that were previously in this vehicle
                    priority_score = 0 if was_in_this_vehicle else days_since_end

                    orphaned_candidates.append(
                        {
                            "battery_id": battery_id,
                            "source_vin": battery_info["source_vin"],
                            "end_date": battery_info["end_date"],
                            "days_since_end": days_since_end,
                            "was_in_this_vehicle": was_in_this_vehicle,
                            "priority_score": priority_score,
                        }
                    )

        if orphaned_candidates:
            # Sort by priority: first by whether it was in this vehicle, then by recency
            orphaned_candidates.sort(
                key=lambda x: (x["priority_score"], x["days_since_end"])
            )
            best_candidate = orphaned_candidates[0]

            logger.info(
                f"Found orphaned battery {best_candidate['battery_id']} for vehicle {vin}"
            )
            logger.info(
                f"  Last seen in: {best_candidate['source_vin']} on {best_candidate['end_date'].date()}"
            )
            logger.info(f"  Days since end: {best_candidate['days_since_end']}")
            logger.info(
                f"  Was in this vehicle before: {best_candidate['was_in_this_vehicle']}"
            )

            return best_candidate

        return None

    def _create_timeline_extension(
        self,
        battery_id: str,
        vin: str,
        start_date: datetime,
        end_date: Optional[datetime],
        source: str,
        note: str,
    ) -> Dict:
        """Create a timeline extension period."""
        duration_days = None
        if start_date and end_date:
            duration_days = (end_date - start_date).days

        lifecycle_stage = "extended" if end_date else "active_extended"
        return {
            "battery_id": battery_id,
            "vin": vin,
            "start_date": start_date,
            "end_date": end_date,
            "adjusted_start_date": None,
            "adjusted_end_date": None,
            "duration_days": duration_days,
            "adjusted_duration_days": None,
            "source": source,
            "confidence": "medium",
            "lifecycle_stage": lifecycle_stage,
            "phase_created": "phase4",
            "note": note,
            "km_validated": False,
            "activity_validated": True,  # Based on activity data
            "is_currently_active": (end_date is None or pd.isna(end_date)),
            "status": self._determine_battery_status(
                end_date, lifecycle_stage, "medium"
            ),
        }

    def _update_existing_periods_with_activity_data(self) -> Dict:
        """Update existing periods that should be extended based on activity data.

        CRITICAL FIX: Handle battery uniqueness constraint - a battery can only be in ONE vehicle at any time.
        """
        updates = {
            "periods_extended": 0,
            "end_dates_updated": 0,
            "battery_conflicts_resolved": 0,
        }

        # CRITICAL FIX: Group active periods by battery_id to handle conflicts
        active_periods_by_battery = {}

        for period in self.final_timeline:
            if period["end_date"] is None:  # Currently active periods
                battery_id = period["battery_id"]
                if battery_id not in active_periods_by_battery:
                    active_periods_by_battery[battery_id] = []
                active_periods_by_battery[battery_id].append(period)

        # Process each battery's active periods
        for battery_id, battery_periods in active_periods_by_battery.items():
            if len(battery_periods) == 1:
                # Single active period - handle normally
                period = battery_periods[0]
                vin = period["vin"]
                latest_activity = self._get_vehicle_latest_activity_date(vin)

                if latest_activity:
                    days_since_activity = (datetime.now() - latest_activity).days

                    if days_since_activity <= 30:  # Active within last 30 days
                        # Keep as active
                        existing_note = period.get("note", "")
                        period["note"] = (
                            f"{existing_note} | Confirmed active until {latest_activity.date()}".strip(
                                " |"
                            )
                        )
                        updates["periods_extended"] += 1
                    else:
                        # Set end date to latest activity
                        period["end_date"] = latest_activity
                        period["lifecycle_stage"] = "inactive"
                        existing_note = period.get("note", "")
                        period["note"] = (
                            f"{existing_note} | Ended based on activity data: {latest_activity.date()}".strip(
                                " |"
                            )
                        )
                        updates["end_dates_updated"] += 1

                        if period["start_date"]:
                            period["duration_days"] = (
                                latest_activity - period["start_date"]
                            ).days

            else:
                # CRITICAL FIX: Multiple active periods for same battery - CONFLICT!
                logger.warning(
                    f"Battery {battery_id} has {len(battery_periods)} active periods - resolving conflict"
                )
                updates["battery_conflicts_resolved"] += 1

                # Get latest activity for each vehicle
                period_activities = []
                for period in battery_periods:
                    vin = period["vin"]
                    latest_activity = self._get_vehicle_latest_activity_date(vin)
                    period_activities.append(
                        {
                            "period": period,
                            "vin": vin,
                            "latest_activity": latest_activity,
                            "start_date": period["start_date"] or datetime.min,
                        }
                    )

                # Sort by start date (latest vehicle gets to keep the battery)
                period_activities.sort(key=lambda x: x["start_date"], reverse=True)

                # The vehicle with the LATEST start date gets to keep the battery active
                current_vehicle = period_activities[0]

                # All earlier vehicles must end their periods
                for i, vehicle_data in enumerate(period_activities):
                    period = vehicle_data["period"]
                    vin = vehicle_data["vin"]
                    latest_activity = vehicle_data["latest_activity"]

                    if i == 0:
                        # Current vehicle (latest start date) - handle normally
                        if latest_activity:
                            days_since_activity = (
                                datetime.now() - latest_activity
                            ).days

                            if days_since_activity <= 30:
                                # Keep as active
                                existing_note = period.get("note", "")
                                period["note"] = (
                                    f"{existing_note} | Current vehicle for battery {battery_id} | Confirmed active until {latest_activity.date()}".strip(
                                        " |"
                                    )
                                )
                                updates["periods_extended"] += 1
                            else:
                                # Set end date to latest activity
                                period["end_date"] = latest_activity
                                period["lifecycle_stage"] = "inactive"
                                existing_note = period.get("note", "")
                                period["note"] = (
                                    f"{existing_note} | Current vehicle for battery {battery_id} | Ended based on activity data: {latest_activity.date()}".strip(
                                        " |"
                                    )
                                )
                                updates["end_dates_updated"] += 1

                                if period["start_date"]:
                                    period["duration_days"] = (
                                        latest_activity - period["start_date"]
                                    ).days
                    else:
                        # Earlier vehicles - must end when current vehicle's period started
                        current_start = current_vehicle["start_date"]

                        # End this period just before the current vehicle's period started
                        transfer_end_date = current_start - timedelta(days=1)

                        # But don't end before this vehicle's latest activity (if reasonable)
                        if latest_activity and transfer_end_date < latest_activity:
                            # Use latest activity as end date if it's before current vehicle start
                            if latest_activity < current_start:
                                transfer_end_date = latest_activity
                            else:
                                # Vehicle was active after transfer - use transfer date
                                transfer_end_date = current_start - timedelta(days=1)

                        period["end_date"] = transfer_end_date
                        period["lifecycle_stage"] = "transferred"
                        existing_note = period.get("note", "")
                        period["note"] = (
                            f"{existing_note} | Battery {battery_id} transferred to {current_vehicle['vin']} on {current_start.date()} | Conflict resolved".strip(
                                " |"
                            )
                        )
                        updates["end_dates_updated"] += 1

                        if period["start_date"]:
                            period["duration_days"] = (
                                transfer_end_date - period["start_date"]
                            ).days

                        logger.info(
                            f"  → Ended period in {vin} on {transfer_end_date.date()} due to battery transfer to {current_vehicle['vin']}"
                        )

        return updates

    # =================================================================
    # PHASE 5: FINAL VALIDATION AND QUALITY ASSURANCE
    # =================================================================

    def phase5_final_validation(self) -> Dict:
        """Phase 5: Final comprehensive validation of the battery timeline."""
        logger.info("=== PHASE 5: FINAL VALIDATION AND QUALITY ASSURANCE ===")

        # Reset validation counters in VALIDATION_CONSTANTS
        VALIDATION_CONSTANTS["validation_passed"] = False
        VALIDATION_CONSTANTS["critical_errors"] = 0
        VALIDATION_CONSTANTS["warnings"] = 0
        VALIDATION_CONSTANTS["battery_uniqueness_violations"] = 0
        VALIDATION_CONSTANTS["chronological_errors"] = 0
        VALIDATION_CONSTANTS["overlap_violations"] = 0
        VALIDATION_CONSTANTS["missing_data_periods"] = 0
        VALIDATION_CONSTANTS["activity_mismatches"] = 0
        VALIDATION_CONSTANTS["transfer_logic_errors"] = 0

        validation_errors = []
        validation_warnings = []

        # 1. CRITICAL: Battery Uniqueness Constraint Validation
        logger.info("Validating battery uniqueness constraints...")
        uniqueness_violations = self._validate_battery_uniqueness()
        VALIDATION_CONSTANTS["battery_uniqueness_violations"] = len(
            uniqueness_violations
        )
        if uniqueness_violations:
            VALIDATION_CONSTANTS["critical_errors"] += len(uniqueness_violations)
            validation_errors.extend(uniqueness_violations)
            logger.error(
                f"❌ Found {len(uniqueness_violations)} battery uniqueness violations"
            )

        # 2. Chronological Consistency Validation
        logger.info("Validating chronological consistency...")
        chronological_errors = self._validate_chronological_consistency()
        VALIDATION_CONSTANTS["chronological_errors"] = len(chronological_errors)
        if chronological_errors:
            VALIDATION_CONSTANTS["critical_errors"] += len(chronological_errors)
            validation_errors.extend(chronological_errors)
            logger.error(f"❌ Found {len(chronological_errors)} chronological errors")

        # 3. Period Overlap Validation
        logger.info("Validating period overlaps...")
        overlap_violations = self._detect_period_overlaps()
        VALIDATION_CONSTANTS["overlap_violations"] = len(overlap_violations)
        if overlap_violations:
            VALIDATION_CONSTANTS["critical_errors"] += len(overlap_violations)
            validation_errors.extend(overlap_violations)
            logger.error(f"❌ Found {len(overlap_violations)} period overlaps")

        # 4. Transfer Logic Validation
        logger.info("Validating transfer logic...")
        transfer_errors = self._validate_transfer_logic()
        VALIDATION_CONSTANTS["transfer_logic_errors"] = len(transfer_errors)
        if transfer_errors:
            VALIDATION_CONSTANTS["critical_errors"] += len(transfer_errors)
            validation_errors.extend(transfer_errors)
            logger.error(f"❌ Found {len(transfer_errors)} transfer logic errors")

        # 5. Missing Data Validation
        logger.info("Validating missing data...")
        missing_data_issues = self._validate_missing_data()
        VALIDATION_CONSTANTS["missing_data_periods"] = len(missing_data_issues)
        if missing_data_issues:
            VALIDATION_CONSTANTS["warnings"] += len(missing_data_issues)
            validation_warnings.extend(missing_data_issues)
            logger.warning(f"⚠️  Found {len(missing_data_issues)} missing data issues")

        # 6. Activity Alignment Validation
        logger.info("Validating activity alignment...")
        activity_mismatches = self._validate_activity_alignment()
        VALIDATION_CONSTANTS["activity_mismatches"] = len(activity_mismatches)
        if activity_mismatches:
            VALIDATION_CONSTANTS["warnings"] += len(activity_mismatches)
            validation_warnings.extend(activity_mismatches)
            logger.warning(
                f"⚠️  Found {len(activity_mismatches)} activity alignment issues"
            )

        # Determine if validation passed
        VALIDATION_CONSTANTS["validation_passed"] = (
            VALIDATION_CONSTANTS["critical_errors"] == 0
        )

        # Log validation summary
        if VALIDATION_CONSTANTS["validation_passed"]:
            logger.info("✅ All critical validations PASSED")
            if VALIDATION_CONSTANTS["warnings"] > 0:
                logger.warning(
                    f"⚠️  {VALIDATION_CONSTANTS['warnings']} warnings found (non-critical)"
                )
        else:
            logger.error(
                f"❌ Validation FAILED: {VALIDATION_CONSTANTS['critical_errors']} critical errors"
            )

        # Store validation details for reporting
        VALIDATION_CONSTANTS["validation_errors"] = validation_errors
        VALIDATION_CONSTANTS["validation_warnings"] = validation_warnings

        logger.info(f"Phase 5 complete: {VALIDATION_CONSTANTS}")
        return VALIDATION_CONSTANTS.copy()

    def _validate_battery_uniqueness(self) -> List[Dict]:
        """Validate that each battery only appears in one vehicle at any given time."""
        violations = []

        # Group periods by battery_id
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        for battery_id, periods in battery_groups.items():
            if len(periods) <= 1:
                continue

            # Sort periods by start date
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            # Check for overlaps
            for i in range(len(periods) - 1):
                current = periods[i]
                next_period = periods[i + 1]

                current_end = current["end_date"] or datetime.max
                next_start = next_period["start_date"] or datetime.min

                # Check for overlap
                if current_end > next_start:
                    violations.append(
                        {
                            "type": "battery_uniqueness_violation",
                            "battery_id": battery_id,
                            "current_period": {
                                "vin": current["vin"],
                                "start_date": current["start_date"],
                                "end_date": current["end_date"],
                            },
                            "overlapping_period": {
                                "vin": next_period["vin"],
                                "start_date": next_period["start_date"],
                                "end_date": next_period["end_date"],
                            },
                            "overlap_days": (current_end - next_start).days,
                            "message": f"Battery {battery_id} appears in {current['vin']} and {next_period['vin']} simultaneously",
                        }
                    )

            # CRITICAL: Check for multiple active periods (end_date = None)
            active_periods = [p for p in periods if p["end_date"] is None]
            if len(active_periods) > 1:
                vins = [p["vin"] for p in active_periods]
                violations.append(
                    {
                        "type": "multiple_active_periods",
                        "battery_id": battery_id,
                        "active_vins": vins,
                        "active_periods": active_periods,
                        "message": f"Battery {battery_id} has multiple active periods in: {', '.join(vins)}",
                    }
                )

        return violations

    def _detect_period_overlaps(self) -> List[Dict]:
        """Detect any overlapping periods for the same battery."""
        overlaps = []

        # Group by battery_id and check for overlaps
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        for battery_id, periods in battery_groups.items():
            if len(periods) <= 1:
                continue

            # Sort by start date
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            # Check each pair for overlaps
            for i in range(len(periods)):
                for j in range(i + 1, len(periods)):
                    period1 = periods[i]
                    period2 = periods[j]

                    # Get date ranges
                    start1 = period1["start_date"]
                    end1 = period1["end_date"] or datetime.now()
                    start2 = period2["start_date"]
                    end2 = period2["end_date"] or datetime.now()

                    # Skip if either has missing start date
                    if not start1 or not start2:
                        continue

                    # Check for overlap: periods overlap if start1 < end2 and start2 < end1
                    if start1 < end2 and start2 < end1:
                        overlap_start = max(start1, start2)
                        overlap_end = min(end1, end2)
                        overlap_days = (overlap_end - overlap_start).days

                        if overlap_days > 0:  # Only report meaningful overlaps
                            overlaps.append(
                                {
                                    "type": "period_overlap",
                                    "battery_id": battery_id,
                                    "period1": {
                                        "vin": period1["vin"],
                                        "start_date": start1,
                                        "end_date": period1["end_date"],
                                    },
                                    "period2": {
                                        "vin": period2["vin"],
                                        "start_date": start2,
                                        "end_date": period2["end_date"],
                                    },
                                    "overlap_start": overlap_start,
                                    "overlap_end": overlap_end,
                                    "overlap_days": overlap_days,
                                    "message": f"Battery {battery_id} overlaps between {period1['vin']} and {period2['vin']} for {overlap_days} days",
                                }
                            )

        return overlaps

    def _validate_chronological_consistency(self) -> List[Dict]:
        """Validate that start_date <= end_date for all periods."""
        errors = []

        for period in self.final_timeline:
            start_date = period["start_date"]
            end_date = period["end_date"]

            # Check basic chronological order
            if start_date and end_date and start_date > end_date:
                errors.append(
                    {
                        "type": "chronological_error",
                        "battery_id": period["battery_id"],
                        "vin": period["vin"],
                        "start_date": start_date,
                        "end_date": end_date,
                        "days_negative": (start_date - end_date).days,
                        "message": f"Period for battery {period['battery_id']} in {period['vin']} has start_date > end_date",
                    }
                )

            # Check adjusted dates consistency
            adj_start = period.get("adjusted_start_date")
            adj_end = period.get("adjusted_end_date")

            if adj_start and adj_end and adj_start > adj_end:
                errors.append(
                    {
                        "type": "adjusted_chronological_error",
                        "battery_id": period["battery_id"],
                        "vin": period["vin"],
                        "adjusted_start_date": adj_start,
                        "adjusted_end_date": adj_end,
                        "days_negative": (adj_start - adj_end).days,
                        "message": f"Period for battery {period['battery_id']} in {period['vin']} has adjusted_start_date > adjusted_end_date",
                    }
                )

            # Check future dates
            now = datetime.now()
            if start_date and start_date > now + timedelta(days=1):
                errors.append(
                    {
                        "type": "future_start_date",
                        "battery_id": period["battery_id"],
                        "vin": period["vin"],
                        "start_date": start_date,
                        "days_future": (start_date - now).days,
                        "message": f"Period for battery {period['battery_id']} in {period['vin']} starts in the future",
                    }
                )

        return errors

    def _validate_transfer_logic(self) -> List[Dict]:
        """Validate that battery transfers between vehicles follow logical patterns."""
        errors = []

        # Group periods by battery_id
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        for battery_id, periods in battery_groups.items():
            if len(periods) <= 1:
                continue

            # Sort periods chronologically
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            # Check transfer logic between consecutive periods
            for i in range(len(periods) - 1):
                current = periods[i]
                next_period = periods[i + 1]

                # Skip if missing critical data
                if not current["start_date"] or not next_period["start_date"]:
                    continue

                current_end = current["end_date"]
                next_start = next_period["start_date"]

                # Case 1: Current period has no end date but next period starts
                if current_end is None and next_start:
                    errors.append(
                        {
                            "type": "transfer_without_end",
                            "battery_id": battery_id,
                            "from_vin": current["vin"],
                            "to_vin": next_period["vin"],
                            "current_period": current,
                            "next_period": next_period,
                            "message": f"Battery {battery_id} transfers from {current['vin']} to {next_period['vin']} but current period has no end date",
                        }
                    )

                # Case 2: Large gap between periods (potential missing data)
                elif current_end and next_start:
                    gap_days = (next_start - current_end).days
                    if gap_days > 30:
                        errors.append(
                            {
                                "type": "large_transfer_gap",
                                "battery_id": battery_id,
                                "from_vin": current["vin"],
                                "to_vin": next_period["vin"],
                                "gap_days": gap_days,
                                "gap_start": current_end,
                                "gap_end": next_start,
                                "message": f"Battery {battery_id} has {gap_days} day gap between {current['vin']} and {next_period['vin']}",
                            }
                        )

                # Case 3: Negative gap (overlap already caught above, but check logic)
                elif current_end and next_start and current_end > next_start:
                    overlap_days = (current_end - next_start).days
                    errors.append(
                        {
                            "type": "transfer_overlap",
                            "battery_id": battery_id,
                            "from_vin": current["vin"],
                            "to_vin": next_period["vin"],
                            "overlap_days": overlap_days,
                            "message": f"Battery {battery_id} transfer overlap: {current['vin']} ends after {next_period['vin']} starts",
                        }
                    )

        return errors

    def _validate_missing_data(self) -> List[Dict]:
        """Identify periods with missing critical data."""
        issues = []

        for period in self.final_timeline:
            period_issues = []

            # Check for missing start date
            if not period["start_date"]:
                period_issues.append("missing_start_date")

            # Check for periods that should probably have end dates but don't
            if not period["end_date"]:
                # Check if this battery appears in later periods (suggesting this should have ended)
                battery_id = period["battery_id"]
                later_periods = [
                    p
                    for p in self.final_timeline
                    if p["battery_id"] == battery_id
                    and p["start_date"]
                    and period["start_date"]
                    and p["start_date"] > period["start_date"]
                ]
                if later_periods:
                    period_issues.append("missing_end_date_with_later_periods")

            # Check for very short durations (potential data issues)
            if period["start_date"] and period["end_date"]:
                duration = (period["end_date"] - period["start_date"]).days
                if duration < 1:
                    period_issues.append("very_short_duration")
                elif duration > 365 * 3:  # More than 3 years
                    period_issues.append("very_long_duration")

            # Check confidence levels
            if period.get("confidence") == "low":
                period_issues.append("low_confidence")

            if period_issues:
                issues.append(
                    {
                        "type": "missing_data",
                        "battery_id": period["battery_id"],
                        "vin": period["vin"],
                        "issues": period_issues,
                        "period": period,
                        "message": f"Period for battery {period['battery_id']} in {period['vin']} has data issues: {', '.join(period_issues)}",
                    }
                )

        return issues

    def _validate_activity_alignment(self) -> List[Dict]:
        """Validate that battery periods align with vehicle activity data."""
        mismatches = []

        for period in self.final_timeline:
            vin = period["vin"]
            start_date = period["start_date"]
            end_date = period["end_date"]

            if not start_date:
                continue

            # Check if vehicle has activity data
            if vin not in self.vin_to_vehicle_id:
                continue

            # Get vehicle activity summary
            activity_summary = self._query_vehicle_activity_on_fly(vin, "summary")
            if not activity_summary:
                continue

            # Check alignment issues
            issues = []

            # Issue 1: Battery period starts before vehicle first activity
            if (
                activity_summary["first_activity_date"]
                and not pd.isna(activity_summary["first_activity_date"])
                and start_date
                and pd.notna(start_date)
            ):
                try:
                    if start_date.date() < activity_summary["first_activity_date"]:
                        days_before = (
                            activity_summary["first_activity_date"] - start_date.date()
                        ).days
                        if days_before > 30:  # More than 30 days before first activity
                            issues.append(
                                f"starts_{days_before}d_before_first_activity"
                            )
                except (TypeError, AttributeError) as e:
                    logger.debug(
                        f"Error comparing dates for battery {period['battery_id']}: {e}"
                    )

            # Issue 2: Battery period ends after vehicle last activity (for ended periods)
            if (
                end_date
                and pd.notna(end_date)
                and activity_summary["last_activity_date"]
                and not pd.isna(activity_summary["last_activity_date"])
            ):
                try:
                    if end_date.date() > activity_summary["last_activity_date"]:
                        days_after = (
                            end_date.date() - activity_summary["last_activity_date"]
                        ).days
                        if days_after > 30:  # More than 30 days after last activity
                            issues.append(f"ends_{days_after}d_after_last_activity")
                except (TypeError, AttributeError) as e:
                    logger.debug(
                        f"Error comparing end dates for battery {period['battery_id']}: {e}"
                    )

            # Issue 3: Very low activity during battery period
            if period.get("activity_validated") is False:
                issues.append("low_activity_during_period")

            # Issue 4: No meaningful km activity but long period
            if end_date:
                duration = (end_date - start_date).days
                if duration > 90 and activity_summary.get("avg_daily_km", 0) < 1:
                    issues.append("long_period_with_minimal_activity")

            if issues:
                mismatches.append(
                    {
                        "type": "activity_alignment_mismatch",
                        "battery_id": period["battery_id"],
                        "vin": vin,
                        "issues": issues,
                        "period_start": start_date,
                        "period_end": end_date,
                        "activity_summary": activity_summary,
                        "message": f"Battery {period['battery_id']} in {vin} has activity alignment issues: {', '.join(issues)}",
                    }
                )

        return mismatches

    def _validate_battery_gap_coverage(
        self, battery_id: str, vin: str, gap_start: datetime, gap_end: datetime
    ) -> bool:
        """
        Validate that a specific battery was continuously in a vehicle during a gap period.

        This ensures that when we use an earlier installation date, the battery was actually
        in that vehicle continuously and no other battery was installed during the gap.

        Args:
            battery_id: The battery ID to validate
            vin: The vehicle VIN
            gap_start: Start of the gap period (earlier installation date)
            gap_end: End of the gap period (later installation date)

        Returns:
            True if the battery can safely use the earlier start date, False otherwise
        """

        # Check 1: Ensure no other battery was installed in this VIN during the gap period
        gap_installations = [
            e
            for e in self.cleaned_events
            if e["vin"] == vin
            and e.get("battery_id_new")  # Installation events
            and e.get("battery_id_new") != battery_id  # Different battery
            and gap_start <= e["date"] <= gap_end  # Within gap period
        ]

        if gap_installations:
            logger.warning(
                f"❌ Gap validation failed for battery {battery_id} in VIN {vin}: "
                f"Found {len(gap_installations)} other battery installations during gap period "
                f"({gap_start.date()} to {gap_end.date()})"
            )
            for installation in gap_installations:
                logger.warning(
                    f"   Other battery {installation['battery_id_new']} installed on {installation['date'].date()}"
                )
            return False

        # Check 2: Ensure this battery was not removed from this VIN during the gap period
        # (unless it was immediately reinstalled)
        gap_removals = [
            e
            for e in self.cleaned_events
            if e["vin"] == vin
            and e.get("battery_id_old") == battery_id  # This battery being removed
            and gap_start < e["date"] < gap_end  # Within gap period (exclusive)
        ]

        if gap_removals:
            logger.warning(
                f"❌ Gap validation failed for battery {battery_id} in VIN {vin}: "
                f"Battery was removed {len(gap_removals)} times during gap period "
                f"({gap_start.date()} to {gap_end.date()})"
            )
            for removal in gap_removals:
                logger.warning(f"   Battery removed on {removal['date'].date()}")
            return False

        # Check 3: Verify that vehicle was actually active during part of the gap period
        # This prevents us from using phantom periods where vehicle wasn't operational
        if vin in self.vin_to_vehicle_id:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id in self.daily_stats_by_vehicle:
                vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

                # Check if vehicle had meaningful activity during the gap period
                gap_activity = vehicle_data[
                    (vehicle_data["date"] >= gap_start.date())
                    & (vehicle_data["date"] <= gap_end.date())
                    & (
                        vehicle_data["km_end"] - vehicle_data["km_start"] >= 2
                    )  # At least 2km movement
                ]

                if len(gap_activity) == 0:
                    logger.info(
                        f"⚠️ Gap validation warning for battery {battery_id} in VIN {vin}: "
                        f"No vehicle activity found during gap period ({gap_start.date()} to {gap_end.date()}). "
                        f"Proceeding with caution."
                    )
                    # Don't fail validation, but lower confidence
                    return True
                else:
                    logger.info(
                        f"✅ Gap validation passed for battery {battery_id} in VIN {vin}: "
                        f"Vehicle had {len(gap_activity)} active days during gap period "
                        f"({gap_start.date()} to {gap_end.date()})"
                    )
                    return True

        # If we can't validate activity data, allow it but with caution
        logger.info(
            f"⚠️ Gap validation incomplete for battery {battery_id} in VIN {vin}: "
            f"No activity data available. Proceeding with caution."
        )
        return True

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Enhanced battery start finding with activity-based validation."""

        # Priority 1: Direct installation events for this specific battery-VIN combination
        # CRITICAL FIX: Find ALL installation events for this battery-VIN pair and use the earliest
        install_events_same_vin = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and e["vin"] == vin  # Same VIN
            and (not before_date or e["date"] < before_date)
        ]

        # Also check for installation events from other VINs (cross-VIN transfers)
        install_events_other_vins = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and e["vin"] != vin  # Different VIN
            and (not before_date or e["date"] < before_date)
        ]

        if install_events_same_vin:
            # Use the earliest installation date for this specific VIN
            earliest_same_vin = min(install_events_same_vin, key=lambda x: x["date"])

            # CRITICAL FIX: Check if there's an even earlier installation from another VIN
            # that could indicate the battery was transferred to this VIN
            if install_events_other_vins:
                earliest_other_vin = min(
                    install_events_other_vins, key=lambda x: x["date"]
                )

                # If there's an earlier installation in another VIN, check if the battery
                # was continuously in this VIN from that earlier date
                if earliest_other_vin["date"] < earliest_same_vin["date"]:
                    # Validate that no other battery was installed in this VIN during the gap period
                    gap_start = earliest_other_vin["date"]
                    gap_end = earliest_same_vin["date"]

                    if self._validate_battery_gap_coverage(
                        battery_id, vin, gap_start, gap_end
                    ):
                        logger.info(
                            f"🔧 BATTERY START FIX: Using earlier date {gap_start.date()} instead of {earliest_same_vin['date'].date()} "
                            f"for battery {battery_id} in VIN {vin} (gap validated)"
                        )
                        # Use the earlier date but note it came from cross-VIN transfer
                        if self._validate_battery_installation_with_activity(
                            vin, gap_start
                        ):
                            return gap_start, "cross_vin_transfer_validated", "high"
                        else:
                            return gap_start, "cross_vin_transfer_validated", "medium"

            # Use the installation event for this VIN
            if self._validate_battery_installation_with_activity(
                vin, earliest_same_vin["date"]
            ):
                return earliest_same_vin["date"], "installation_event", "high"
            else:
                return earliest_same_vin["date"], "installation_event", "medium"

        # If no direct installation events for this VIN, check cross-VIN installations
        if install_events_other_vins:
            earliest = min(install_events_other_vins, key=lambda x: x["date"])
            # Validate cross-VIN installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "high",
                )
            else:
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "medium",
                )

        # Priority 2: Implied installations (battery_id in old OR new, but not both)
        implied_events = [
            e
            for e in self.cleaned_events
            if (
                # Case 1: battery_id in old, new is empty (removal/confirmation)
                (e["battery_id_old"] == battery_id and not e["battery_id_new"])
                # Case 2: battery_id in new, old is empty (installation without removal)
                or (e["battery_id_new"] == battery_id and not e["battery_id_old"])
            )
            and (not before_date or e["date"] < before_date)
        ]
        if implied_events:
            earliest = min(implied_events, key=lambda x: x["date"])
            # Validate implied installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return earliest["date"], "implied_installation", "high"
            else:
                return earliest["date"], "implied_installation", "medium"

        # Priority 3: Activity-based start date (for vehicles with good activity data)
        activity_start = self._find_activity_based_start_date(vin, before_date)
        if activity_start:
            start_date, method, confidence = activity_start
            return start_date, method, confidence

        # Priority 4: Cross-VIN snapshot matching with activity validation
        if battery_id in self.global_battery_cache:
            vins_used = self.global_battery_cache[battery_id]["vins_used"]

            for other_vin in vins_used:
                if other_vin == vin:
                    continue

                other_vehicle_info = self.vehicle_info_cache.get(other_vin, {})
                other_snapshot_batteries = [
                    other_vehicle_info.get("master_battery"),
                    other_vehicle_info.get("slave_battery"),
                ]

                if battery_id in other_snapshot_batteries:
                    # Check if there are any installation events for this battery in the other VIN
                    other_vin_installations = [
                        e
                        for e in self.cleaned_events
                        if e["battery_id_new"] == battery_id
                        and e["vin"] == other_vin
                        and (not before_date or e["date"] < before_date)
                    ]

                    if other_vin_installations:
                        # Use the most recent installation event instead of snapshot date
                        latest_installation = max(
                            other_vin_installations, key=lambda x: x["date"]
                        )
                        return (
                            latest_installation["date"],
                            f"cross_vin_installation_from_{other_vin}",
                            "high",
                        )
                    else:
                        # Try activity-based start for cross-VIN scenario first
                        activity_start = self._find_activity_based_start_date(
                            other_vin, before_date
                        )
                        if activity_start:
                            start_date, method, confidence = activity_start
                            return (
                                start_date,
                                f"cross_vin_snapshot_from_{other_vin}_{method}",
                                confidence,
                            )
                        else:
                            # Fallback to erstzulassung for cross-VIN if no activity data
                            erstzulassung = other_vehicle_info.get("erstzulassung")
                            if (
                                erstzulassung
                                and not pd.isna(erstzulassung)
                                and (not before_date or erstzulassung < before_date)
                            ):
                                return (
                                    erstzulassung,
                                    f"cross_vin_snapshot_from_{other_vin}_erstzulassung",
                                    "medium",
                                )

        # Priority 5: Snapshot matching with activity validation (fallback for current VIN)
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        if battery_id in snapshot_batteries:
            # First check if there are installation events for this battery in the current VIN
            current_vin_installations = [
                e
                for e in self.cleaned_events
                if e["battery_id_new"] == battery_id
                and e["vin"] == vin
                and (not before_date or e["date"] < before_date)
            ]

            if current_vin_installations:
                # Use the most recent installation event - this is actual event data (high priority)
                latest_installation = max(
                    current_vin_installations, key=lambda x: x["date"]
                )
                if self._validate_battery_installation_with_activity(
                    vin, latest_installation["date"]
                ):
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "high",
                    )
                else:
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "medium",
                    )

            # Fallback to erstzulassung only if no installation events exist
            erstzulassung = vehicle_info.get("erstzulassung")
            # Ensure erstzulassung is not NaT before using
            if (
                erstzulassung
                and not pd.isna(erstzulassung)
                and (not before_date or erstzulassung < before_date)
            ):
                # Try activity-based start first
                activity_start = self._find_activity_based_start_date(vin, before_date)
                if activity_start:
                    start_date, method, confidence = activity_start
                    return start_date, f"snapshot_activity_{method}", confidence
                else:
                    # Check if VIN has poor activity data quality
                    if vin in self.vehicle_activity_cache:
                        activity = self.vehicle_activity_cache[vin]
                        if activity["data_quality_score"] < 0.5:  # Poor activity data
                            return (
                                erstzulassung,
                                "snapshot_erstzulassung_poor_activity",
                                "medium",
                            )

                    # Fallback to erstzulassung if no activity data
                    return erstzulassung, "snapshot_erstzulassung_no_activity", "low"

        # Priority 6: Global battery cache fallback
        if battery_id in self.global_battery_cache:
            cache_entry = self.global_battery_cache[battery_id]
            earliest_date = cache_entry["first_seen"]
            if not before_date or earliest_date < before_date:
                return earliest_date, "global_cache_earliest", "low"

        return None, "no_start_found", "low"

    def _check_battery_timeline_overlap(
        self,
        battery_id: str,
        vin: str,
        start_date: datetime,
        end_date: Optional[datetime],
    ) -> Optional[Dict]:
        """Check if creating a new period would overlap with existing battery timeline.

        Args:
            battery_id: The battery ID to check
            vin: The vehicle VIN
            start_date: Proposed start date for new period
            end_date: Proposed end date for new period (None = ongoing)

        Returns:
            Dict with conflict details if overlap detected, None otherwise
        """
        # Check all existing periods for this battery
        for period in self.final_timeline:
            if period["battery_id"] == battery_id:
                existing_start = period["start_date"]
                existing_end = (
                    period["end_date"] or datetime.now()
                )  # Treat None as current time

                # Skip if existing period has no start date
                if not existing_start:
                    continue

                # Calculate proposed end date for overlap checking
                proposed_end = end_date or datetime.now()

                # Check for overlap: periods overlap if start1 < end2 and start2 < end1
                if start_date < existing_end and existing_start < proposed_end:
                    overlap_start = max(start_date, existing_start)
                    overlap_end = min(proposed_end, existing_end)
                    overlap_days = (overlap_end - overlap_start).days

                    if overlap_days > 0:
                        return {
                            "type": "battery_timeline_overlap",
                            "battery_id": battery_id,
                            "proposed_vin": vin,
                            "existing_vin": period["vin"],
                            "proposed_start": start_date.date(),
                            "proposed_end": end_date.date() if end_date else "ongoing",
                            "existing_start": existing_start.date(),
                            "existing_end": (
                                period["end_date"].date()
                                if period["end_date"]
                                else "ongoing"
                            ),
                            "overlap_days": overlap_days,
                            "message": f"Battery {battery_id} new period in {vin} ({start_date.date()} to {'ongoing' if not end_date else end_date.date()}) would overlap {overlap_days} days with existing period in {period['vin']} ({existing_start.date()} to {'ongoing' if not period['end_date'] else period['end_date'].date()})",
                        }

        return None

    # Add new validation method after line 2097
    def _validate_vehicle_level_chronology(
        self,
        battery_id: str,
        vin: str,
        proposed_start: datetime,
        proposed_end: Optional[datetime],
        existing_periods: List[Dict] = None,
    ) -> Tuple[Optional[datetime], Optional[datetime], bool]:
        """
        Validate that a proposed battery period doesn't overlap with other battery periods in the same vehicle.

        CRITICAL FIX for the orphaned battery issue:
        When we find a start date for an orphaned removal, we must ensure it doesn't overlap
        with other batteries that were in this vehicle.

        Args:
            battery_id: The battery ID being validated
            vin: The vehicle VIN
            proposed_start: Proposed start date
            proposed_end: Proposed end date (None = ongoing)

        Returns:
            Tuple of (adjusted_start, adjusted_end, is_valid)
            - adjusted_start: Safe start date that doesn't overlap
            - adjusted_end: Safe end date that doesn't overlap
            - is_valid: True if a valid non-overlapping period can be created
        """

        # Get all existing periods for this vehicle (excluding the current battery)
        if existing_periods:
            # Use periods being built during Phase 2 processing
            vehicle_periods = [
                p
                for p in existing_periods
                if p["vin"] == vin and p["battery_id"] != battery_id
            ]
        else:
            # Use final timeline for other phases
            vehicle_periods = [
                p
                for p in self.final_timeline
                if p["vin"] == vin and p["battery_id"] != battery_id
            ]

        if not vehicle_periods:
            # No other periods in this vehicle - proposed period is valid
            return proposed_start, proposed_end, True

        # Sort vehicle periods by start date
        vehicle_periods.sort(key=lambda x: x["start_date"] or datetime.min)

        logger.debug(f"🔍 CHRONOLOGY CHECK for battery {battery_id} in {vin}:")
        logger.debug(
            f"   Proposed period: {proposed_start.date() if proposed_start else 'None'} to {proposed_end.date() if proposed_end else 'ongoing'}"
        )

        for i, period in enumerate(vehicle_periods):
            period_start = period["start_date"]
            period_end = period["end_date"] or datetime.now()
            logger.debug(
                f"   Existing period {i+1}: {period['battery_id']} | {period_start.date() if period_start else 'None'} to {period['end_date'].date() if period['end_date'] else 'ongoing'}"
            )

        # Find chronologically safe boundaries for the proposed period
        safe_start = proposed_start
        safe_end = proposed_end

        # Check each existing period for potential conflicts
        for period in vehicle_periods:
            period_start = period["start_date"]
            period_end = period["end_date"]

            if not period_start:
                continue  # Skip periods without start dates

            # Case 1: If proposed period would start before an existing period starts
            if proposed_start and proposed_start < period_start:
                # The proposed period must end before the existing period starts
                if not proposed_end or proposed_end > period_start:
                    logger.info(
                        f"   🔧 CHRONOLOGY FIX: Adjusting end date from {proposed_end.date() if proposed_end else 'ongoing'} to {period_start.date()} to avoid overlap with battery {period['battery_id']}"
                    )
                    safe_end = period_start  # End exactly when next battery starts

            # Case 2: If proposed period would start after an existing period starts
            elif proposed_start and period_start <= proposed_start:
                # The proposed period must start after the existing period ends
                if period_end and proposed_start < period_end:
                    logger.info(
                        f"   🔧 CHRONOLOGY FIX: Adjusting start date from {proposed_start.date()} to {period_end.date()} to avoid overlap with battery {period['battery_id']}"
                    )
                    safe_start = period_end  # Start exactly when previous battery ends
                elif not period_end:
                    # Existing period is ongoing - proposed period cannot start
                    logger.warning(
                        f"   ❌ CHRONOLOGY CONFLICT: Cannot create period for battery {battery_id} - battery {period['battery_id']} is currently active in {vin}"
                    )
                    return None, None, False

        # CRITICAL FIX: Instead of rejecting, adjust periods to make them sequential
        if safe_start and safe_end and safe_start >= safe_end:
            logger.warning(
                f"   🔧 CHRONOLOGY ADJUSTMENT: Invalid period for battery {battery_id} in {vin} (start {safe_start.date()} >= end {safe_end.date()})"
            )

            # For orphaned removals, the end date is fixed (removal date), so adjust based on other periods
            if proposed_end:  # This is an orphaned removal with fixed end date
                # Find the latest period that ends before the proposed end date
                latest_preceding_end = None
                for period in vehicle_periods:
                    period_end = period["end_date"]
                    if period_end and period_end < proposed_end:
                        if (
                            latest_preceding_end is None
                            or period_end > latest_preceding_end
                        ):
                            latest_preceding_end = period_end

                if latest_preceding_end:
                    # Start this period exactly when the preceding period ends
                    safe_start = latest_preceding_end
                    safe_end = proposed_end  # Keep original end date
                    logger.info(
                        f"   🔧 CHRONOLOGY FIX: Adjusted start date to {safe_start.date()} to start exactly when preceding period ends"
                    )
                else:
                    # No preceding period, use a reasonable fallback start date
                    # Start 1 year before the end date as a reasonable period
                    safe_start = proposed_end - timedelta(days=365)
                    safe_end = proposed_end
                    logger.info(
                        f"   🔧 CHRONOLOGY FIX: Using fallback period {safe_start.date()} to {safe_end.date()}"
                    )
            else:
                # For installations or ongoing periods, reject if adjustment fails
                logger.warning(
                    f"   ❌ CHRONOLOGY CONFLICT: Cannot adjust ongoing period for battery {battery_id} in {vin}"
                )
                return None, None, False

        # Check minimum period duration (at least 1 day) after adjustments
        if safe_start and safe_end:
            duration_days = (safe_end - safe_start).days
            if duration_days < 1:
                logger.warning(
                    f"   ⚠️ CHRONOLOGY WARNING: Very short period for battery {battery_id} in {vin} ({duration_days} days) - allowing with reduced confidence"
                )
                # Allow very short periods but note the issue
                # return None, None, False

        logger.info(
            f"   ✅ CHRONOLOGY VALIDATED: Safe period for battery {battery_id} in {vin}: {safe_start.date() if safe_start else 'None'} to {safe_end.date() if safe_end else 'ongoing'}"
        )
        return safe_start, safe_end, True

    def _find_next_battery_event_in_vehicle(
        self, vin: str, after_date: datetime, all_events: List[Dict]
    ) -> Optional[Dict]:
        """
        Find the next battery event in the same vehicle after a specific date.

        This is crucial for determining if an orphaned removal is actually:
        1. An implied installation (next event has same battery_id_old)
        2. An actual removal (next event has different battery_id_old)

        Args:
            vin: Vehicle VIN to search in
            after_date: Find events after this date
            all_events: All events to search through

        Returns:
            The next battery event in this vehicle, or None if no next event
        """
        # Find all battery events in this vehicle after the given date
        later_events = [
            event
            for event in all_events
            if event["vin"] == vin
            and event["date"] > after_date
            and (
                event.get("battery_id_old") or event.get("battery_id_new")
            )  # Must involve a battery
        ]

        if not later_events:
            return None

        # Sort by date and return the earliest (next) event
        later_events.sort(key=lambda x: x["date"])
        return later_events[0]

    def _update_existing_battery_start_date(
        self,
        battery_id: str,
        vin: str,
        new_start_date: datetime,
        vehicle_period_cache: Dict,
        periods: List[Dict],
    ):
        """
        Update the start date of a battery period that was already processed.

        This handles the case where battery A creates a handoff for battery B,
        but battery B was already processed earlier with a different start date.
        """
        # Check in the current battery's periods first
        for period in periods:
            if period["battery_id"] == battery_id and period["vin"] == vin:
                old_start = period["start_date"]
                if old_start and old_start < new_start_date:
                    logger.info(
                        f"🔧 RETROACTIVE HANDOFF: Updating start date for battery {battery_id} in {vin} from {old_start.date()} to {new_start_date.date()}"
                    )
                    period["start_date"] = new_start_date

                    # Update duration if there's an end date
                    if period["end_date"]:
                        period["duration_days"] = (
                            period["end_date"] - new_start_date
                        ).days

                    # Update note to reflect the change
                    existing_note = period.get("note", "")
                    handoff_note = f"Retroactive handoff: start adjusted to {new_start_date.date()}"
                    period["note"] = f"{existing_note} | {handoff_note}".strip(" |")
                    return True

        # Check in the vehicle cache
        if vin in vehicle_period_cache:
            for period in vehicle_period_cache[vin]:
                if period["battery_id"] == battery_id:
                    old_start = period["start_date"]
                    if old_start and old_start < new_start_date:
                        logger.info(
                            f"🔧 RETROACTIVE HANDOFF (cached): Updating start date for battery {battery_id} in {vin} from {old_start.date()} to {new_start_date.date()}"
                        )
                        period["start_date"] = new_start_date

                        # Update duration if there's an end date
                        if period["end_date"]:
                            period["duration_days"] = (
                                period["end_date"] - new_start_date
                            ).days

                        # Update note to reflect the change
                        existing_note = period.get("note", "")
                        handoff_note = f"Retroactive handoff: start adjusted to {new_start_date.date()}"
                        period["note"] = f"{existing_note} | {handoff_note}".strip(" |")
                        return True

        return False


def main():
    """Main function to run streamlined battery timeline analysis."""

    hv_repair_file = "hv_repair_2025-06-02b.csv"
    working_matching_vehicles_file = "comparison_results/working_matching_vehicles.csv"
    working_unique_vehicles_file = "comparison_results/working_unique_vehicles.csv"
    daily_stats_csv_file = "daily_stats.csv"

    # Test batteries for quick testing (set to None for full processing)
    # test_batteries = None  # Enable full processing with activity validation

    test_batteries = ["V6P0116B000AA01458", "V6P0116B000AA03157"]
    # test_batteries = None
    pipeline = BatteryTimelinePipeline(
        hv_repair_file,
        working_matching_vehicles_file,
        working_unique_vehicles_file,
        daily_stats_csv_file,
        test_batteries=test_batteries,  # Enable test mode
    )

    try:
        print("STREAMLINED BATTERY TIMELINE ANALYSIS (5-PHASE) - HYBRID MODE")
        print("=" * 65)
        if test_batteries:
            print(
                f"🧪 TEST MODE: Processing only {len(test_batteries)} specific batteries"
            )
            print(f"Test batteries: {', '.join(test_batteries)}")
            print()
        else:
            print(
                "🚀 FULL PROCESSING MODE: Processing all batteries with comprehensive validation"
            )
            print()
        print("Streamlined 5-Phase Approach:")
        print("  Phase 1: Data Loading and Cleaning (Hybrid CSV + PostgreSQL)")
        print("  Phase 2: Unified Timeline Building (Battery-Centric)")
        print("  Phase 3: Comprehensive Validation and Quality Assurance")
        print("  Phase 4: Vehicle Activity Validation and Timeline Extension")
        print("  Phase 5: Final Validation and Quality Assurance")
        print()
        print("Hybrid Data Sources:")
        print("  ✓ Daily stats activity data: Loaded from CSV for performance")
        print("  ✓ VIN to vehicle_id mapping: Loaded from PostgreSQL vehicles table")
        print("  ✓ Activity validation: Using pre-loaded CSV data in memory")
        print("  ✓ Battery-centric processing with descending date sorting")
        print("  ✓ Inline edge case handling")
        print("  ✓ Enhanced gap inference using fleet patterns")
        print()

        # Run streamlined analysis with mandatory database check
        results = pipeline.run_analysis_with_db_check()

        if results["success"]:
            # Export results
            output_dir = (
                "test_battery_results" if test_batteries else "battery_timeline_results"
            )
            export_files = pipeline.export_results(output_dir)

            # Display summary
            print(pipeline.generate_summary_report())
            print(f"\nResults exported to: {export_files}")
        else:
            print(f"❌ Analysis failed: {results.get('error', 'Unknown error')}")

            # If database connection failed, provide guidance
            if "database" in str(results.get("error", "")).lower():
                print("\n" + "=" * 60)
                print("DATABASE CONNECTION TROUBLESHOOTING")
                print("=" * 60)
                print("The pipeline requires PostgreSQL for VIN to vehicle_id mapping.")
                print("Daily stats are now loaded from CSV for better performance.")
                print(
                    f"Connection string used: {results.get('db_connection_string', 'N/A')}"
                )
                print("\nOptions to resolve:")
                print("1. Start PostgreSQL server and retry")
                print("2. Update connection parameters via environment variables")
                print("3. Run in legacy mode (without database validation):")
                print("   pipeline.run_analysis_without_db_validation()")
                print("=" * 60)

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main()
